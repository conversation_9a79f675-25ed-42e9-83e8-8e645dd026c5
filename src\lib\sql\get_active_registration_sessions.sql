-- Function to get active registration sessions for a specific device
CREATE OR REPLACE FUNCTION get_active_registration_sessions(device_id_param TEXT)
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    result JSON;
BEGIN
    -- Get active registration sessions for the specified device
    SELECT json_agg(
        json_build_object(
            'id', brs.id,
            'session_name', brs.session_name,
            'device_id', brs.device_id,
            'device_name', brs.device_name,
            'status', brs.status,
            'created_at', brs.created_at,
            'updated_at', brs.updated_at,
            'total_students', brs.total_students,
            'registered_students', brs.registered_students,
            'failed_students', brs.failed_students,
            'progress', json_build_object(
                'total', COALESCE(student_stats.total, 0),
                'registered', COALESCE(student_stats.registered, 0),
                'failed', COALESCE(student_stats.failed, 0),
                'pending', COALESCE(student_stats.pending, 0),
                'percentage', CASE 
                    WHEN COALESCE(student_stats.total, 0) > 0 
                    THEN ROUND((COALESCE(student_stats.registered, 0)::DECIMAL / student_stats.total) * 100)
                    ELSE 0 
                END
            ),
            'biometric_registration_students', COALESCE(students_array, '[]'::json)
        )
    ) INTO result
    FROM biometric_registration_sessions brs
    LEFT JOIN (
        -- Get student statistics for each session
        SELECT 
            session_id,
            COUNT(*) as total,
            COUNT(*) FILTER (WHERE status = 'registered') as registered,
            COUNT(*) FILTER (WHERE status = 'failed') as failed,
            COUNT(*) FILTER (WHERE status = 'pending') as pending
        FROM biometric_registration_students
        GROUP BY session_id
    ) student_stats ON brs.id = student_stats.session_id
    LEFT JOIN (
        -- Get students array for each session
        SELECT 
            session_id,
            json_agg(
                json_build_object(
                    'id', id,
                    'student_email', student_email,
                    'status', status,
                    'registration_attempts', registration_attempts,
                    'registered_at', registered_at
                )
            ) as students_array
        FROM biometric_registration_students
        GROUP BY session_id
    ) students_data ON brs.id = students_data.session_id
    WHERE brs.device_id = device_id_param 
    AND brs.status = 'active';

    -- Return empty array if no sessions found
    IF result IS NULL THEN
        result := '[]'::json;
    END IF;

    RETURN result;
END;
$$;
