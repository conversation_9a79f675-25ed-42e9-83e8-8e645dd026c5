/**
 * Server-Side Time Validation Service
 * 
 * This service provides secure time validation using server time instead of client time
 * to prevent time manipulation attacks on attendance and excuse systems.
 */

import { supabase } from "@/lib/supabase";

export interface ServerTimeInfo {
  server_timestamp: string;
  server_time: string;
  server_date: string;
  timezone: string;
}

export interface AttendanceTimeValidation {
  is_valid: boolean;
  current_server_time: string;
  recording_start_time: string | null;
  recording_end_time: string | null;
  error_message: string;
}

export interface ExcuseTimeValidation {
  is_valid: boolean;
  current_server_time: string;
  submission_start_time: string | null;
  submission_end_time: string | null;
  error_message: string;
}

export interface AttendanceRecordResult {
  success: boolean;
  attendance_id: string | null;
  server_timestamp: string;
  error_message: string;
}

export interface ExcuseSubmissionResult {
  success: boolean;
  excuse_id: string | null;
  server_timestamp: string;
  error_message: string;
}

/**
 * Get current server time for client synchronization
 */
export async function getServerTime(): Promise<ServerTimeInfo | null> {
  try {
    const { data, error } = await supabase.rpc('get_server_time');
    
    if (error) {
      console.error('Error getting server time:', error);
      return null;
    }
    
    return data[0] as ServerTimeInfo;
  } catch (error) {
    console.error('Failed to get server time:', error);
    return null;
  }
}

/**
 * Validate attendance recording time using server time
 */
export async function validateAttendanceTime(schoolId: string): Promise<AttendanceTimeValidation | null> {
  try {
    const { data, error } = await supabase.rpc('validate_attendance_time', {
      school_id_param: schoolId
    });
    
    if (error) {
      console.error('Error validating attendance time:', error);
      return null;
    }
    
    return data[0] as AttendanceTimeValidation;
  } catch (error) {
    console.error('Failed to validate attendance time:', error);
    return null;
  }
}

/**
 * Validate excuse submission time using server time
 */
export async function validateExcuseSubmissionTime(schoolId: string): Promise<ExcuseTimeValidation | null> {
  try {
    const { data, error } = await supabase.rpc('validate_excuse_submission_time', {
      school_id_param: schoolId
    });
    
    if (error) {
      console.error('Error validating excuse submission time:', error);
      return null;
    }
    
    return data[0] as ExcuseTimeValidation;
  } catch (error) {
    console.error('Failed to validate excuse submission time:', error);
    return null;
  }
}

/**
 * Record attendance with server-side time validation
 */
export async function recordAttendanceSecure(
  studentId: string,
  roomId: string,
  qrSessionId: string,
  verificationMethod: string = 'qr_code',
  deviceInfo?: any,
  location?: any
): Promise<AttendanceRecordResult | null> {
  try {
    const { data, error } = await supabase.rpc('record_attendance_secure', {
      student_id_param: studentId,
      room_id_param: roomId,
      qr_session_id_param: qrSessionId,
      verification_method_param: verificationMethod,
      device_info_param: deviceInfo ? JSON.stringify(deviceInfo) : null,
      location_param: location
    });
    
    if (error) {
      console.error('Error recording attendance:', error);
      return {
        success: false,
        attendance_id: null,
        server_timestamp: new Date().toISOString(),
        error_message: error.message
      };
    }
    
    return data[0] as AttendanceRecordResult;
  } catch (error) {
    console.error('Failed to record attendance:', error);
    return {
      success: false,
      attendance_id: null,
      server_timestamp: new Date().toISOString(),
      error_message: 'Failed to record attendance'
    };
  }
}

/**
 * Submit excuse with server-side time validation
 */
export async function submitExcuseSecure(
  studentId: string,
  startDate: string,
  endDate: string,
  reason: string,
  excuseType: string = 'sick'
): Promise<ExcuseSubmissionResult | null> {
  try {
    const { data, error } = await supabase.rpc('submit_excuse_secure', {
      student_id_param: studentId,
      start_date_param: startDate,
      end_date_param: endDate,
      reason_param: reason,
      excuse_type_param: excuseType
    });
    
    if (error) {
      console.error('Error submitting excuse:', error);
      return {
        success: false,
        excuse_id: null,
        server_timestamp: new Date().toISOString(),
        error_message: error.message
      };
    }
    
    return data[0] as ExcuseSubmissionResult;
  } catch (error) {
    console.error('Failed to submit excuse:', error);
    return {
      success: false,
      excuse_id: null,
      server_timestamp: new Date().toISOString(),
      error_message: 'Failed to submit excuse'
    };
  }
}

/**
 * Format server time for display
 */
export function formatServerTime(timeString: string): string {
  return timeString.substring(0, 5); // HH:MM format
}

/**
 * Check if current server time is within a time range
 * This is a utility function for client-side display purposes only
 * All actual validation should use server-side functions
 */
export function isTimeWithinRange(
  currentTime: string,
  startTime: string,
  endTime: string
): boolean {
  const current = currentTime.substring(0, 5);
  const start = startTime.substring(0, 5);
  const end = endTime.substring(0, 5);
  
  return current >= start && current <= end;
}
