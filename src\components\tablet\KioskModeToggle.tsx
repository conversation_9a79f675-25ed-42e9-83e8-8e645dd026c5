import React from 'react';
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Card, CardContent } from "@/components/ui/card";
import { Fingerprint, QrCode } from "lucide-react";
import { useTranslation } from "react-i18next";

interface KioskModeToggleProps {
  isKioskMode: boolean;
  onToggle: (enabled: boolean) => void;
  className?: string;
}

export default function KioskModeToggle({ isKioskMode, onToggle, className = '' }: KioskModeToggleProps) {
  const { t } = useTranslation();

  return (
    <Card className={`${className}`}>
      <CardContent className="p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            {isKioskMode ? (
              <Fingerprint className="h-5 w-5 text-primary" />
            ) : (
              <QrCode className="h-5 w-5 text-primary" />
            )}
            <div>
              <Label htmlFor="kiosk-mode" className="font-medium">
                {isKioskMode ? t('tablet.studentKioskMode') : t('tablet.qrDisplayMode')}
              </Label>
              <p className="text-xs text-muted-foreground">
                {isKioskMode 
                  ? t('tablet.kioskModeDescription') 
                  : t('tablet.qrModeDescription')}
              </p>
            </div>
          </div>
          <Switch
            id="kiosk-mode"
            checked={isKioskMode}
            onCheckedChange={onToggle}
          />
        </div>
      </CardContent>
    </Card>
  );
}
