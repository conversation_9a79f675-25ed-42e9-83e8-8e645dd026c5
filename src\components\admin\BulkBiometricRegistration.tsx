import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Upload,
  Users,
  Fingerprint,
  CheckCircle,
  XCircle,
  Clock,
  AlertTriangle,
  Download,
  Play,
  Pause,
  RotateCcw,
  Monitor,
  FileText,
  Plus,
  Trash2,
  UserX
} from "lucide-react";
import { useAuth } from "@/context/AuthContext";
import { useToast } from "@/hooks/use-toast";
import { useTranslation } from "react-i18next";
import { supabase } from "@/lib/supabase";
import { getDeviceInfo } from "@/lib/webauthn";
import { tabletAuthService } from "@/lib/services/tablet-auth";

interface Student {
  id: string;
  email: string;
  name: string;
  status: 'pending' | 'registered' | 'failed' | 'skipped';
  error?: string;
  registeredAt?: string;
}

interface RegistrationSession {
  id: string;
  deviceId: string;
  deviceName: string;
  sessionName: string;
  totalStudents: number;
  registeredStudents: number;
  failedRegistrations: number;
  sessionStatus: 'active' | 'completed' | 'cancelled';
  startedAt: string;
  completedAt?: string;
  students: Student[];
}

export default function BulkBiometricRegistration() {
  const [sessions, setSessions] = useState<RegistrationSession[]>([]);
  const [currentSession, setCurrentSession] = useState<RegistrationSession | null>(null);
  const [loading, setLoading] = useState(false);
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [studentEmails, setStudentEmails] = useState('');
  const [sessionName, setSessionName] = useState('');
  const [deviceInfo, setDeviceInfo] = useState<any>(null);

  // Delete confirmation states
  const [deleteSessionId, setDeleteSessionId] = useState<string | null>(null);
  const [deleteStudentData, setDeleteStudentData] = useState<{sessionId: string, studentId: string, studentName: string} | null>(null);
  const [deleting, setDeleting] = useState(false);

  const { user, profile } = useAuth();
  const { toast } = useToast();
  const { t } = useTranslation();

  useEffect(() => {
    // Get device information - prioritize tablet auth service, fallback to webauthn
    const getDeviceInformation = () => {
      const tabletDevice = tabletAuthService.getCurrentDevice();

      if (tabletDevice.deviceId) {
        // Use tablet auth service device info (preferred for consistency)
        return {
          deviceId: tabletDevice.deviceId,
          deviceName: `Admin-${tabletDevice.deviceId.slice(0, 8)}`,
          userAgent: navigator.userAgent,
          platform: navigator.platform
        };
      } else {
        // Fallback to webauthn device info
        const webauthnInfo = getDeviceInfo();
        return webauthnInfo;
      }
    };

    const info = getDeviceInformation();
    setDeviceInfo(info);

    // Load existing sessions
    loadSessions();
  }, []);

  const loadSessions = async () => {
    if (!profile?.school_id) return;

    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('registration_sessions')
        .select(`
          *,
          registration_session_students (
            student_id,
            student_email,
            student_name,
            registration_status,
            registration_order,
            registered_at,
            error_message
          )
        `)
        .eq('school_id', profile.school_id)
        .order('created_at', { ascending: false });

      if (error) throw error;

      const formattedSessions = data.map(session => ({
        id: session.id,
        deviceId: session.device_id,
        deviceName: session.device_name,
        sessionName: session.session_name,
        totalStudents: session.total_students,
        registeredStudents: session.registered_students,
        failedRegistrations: session.failed_registrations,
        sessionStatus: session.session_status,
        startedAt: session.started_at,
        completedAt: session.completed_at,
        students: session.registration_session_students.map((s: any) => ({
          id: s.student_id,
          email: s.student_email,
          name: s.student_name,
          status: s.registration_status,
          error: s.error_message,
          registeredAt: s.registered_at
        })).sort((a: any, b: any) => a.registration_order - b.registration_order)
      }));

      setSessions(formattedSessions);
    } catch (error) {
      console.error('Error loading sessions:', error);
      toast({
        title: t('common.error'),
        description: t("tablet.biometricRegistration.bulkRegistration.registrationSessions.errors.loadSessionsFailed"),
        variant: 'destructive'
      });
    } finally {
      setLoading(false);
    }
  };

  const createSession = async () => {
    if (!profile?.school_id || !deviceInfo) return;

    const emails = studentEmails
      .split('\n')
      .map(email => email.trim())
      .filter(email => email && email.includes('@'));

    if (emails.length === 0) {
      toast({
        title: t('common.error'),
        description: t("tablet.biometricRegistration.bulkRegistration.errors.invalidEmails"),
        variant: 'destructive'
      });
      return;
    }

    try {
      setLoading(true);
      const { data, error } = await supabase.rpc('start_registration_session', {
        device_id_param: deviceInfo.deviceId,
        device_name_param: deviceInfo.deviceName,
        session_name_param: sessionName || `Registration Session ${new Date().toLocaleDateString()}`,
        school_id_param: profile.school_id,
        student_emails: emails
      });

      if (error) throw error;

      if (!data.success) {
        throw new Error(data.error || t("tablet.biometricRegistration.bulkRegistration.errors.createFailed"));
      }

      toast({
        title: t('common.success'),
        description: t("tablet.biometricRegistration.bulkRegistration.success.sessionCreated", { count: data.total_students })
      });

      setShowCreateDialog(false);
      setStudentEmails('');
      setSessionName('');
      loadSessions();
    } catch (error) {
      console.error('Error creating session:', error);
      toast({
        title: t('common.error'),
        description: error instanceof Error ? error.message : t("tablet.biometricRegistration.bulkRegistration.errors.sessionCreationFailed"),
        variant: 'destructive'
      });
    } finally {
      setLoading(false);
    }
  };

  const deleteSession = async (sessionId: string) => {
    try {
      setDeleting(true);
      const { data, error } = await supabase.rpc('delete_registration_session', {
        session_id_param: sessionId
      });

      if (error) throw error;

      if (!data.success) {
        throw new Error(data.error || t("tablet.biometricRegistration.bulkRegistration.registrationSessions.errors.deleteSessionFailed"));
      }

      toast({
        title: t('common.success'),
        description: t("tablet.biometricRegistration.bulkRegistration.registrationSessions.success.sessionDeleted", { count: data.deleted_credentials })
      });

      setDeleteSessionId(null);
      loadSessions();
    } catch (error) {
      console.error('Error deleting session:', error);
      toast({
        title: t('common.error'),
        description: error instanceof Error ? error.message : t("tablet.biometricRegistration.bulkRegistration.registrationSessions.errors.deleteSessionFailed"),
        variant: 'destructive'
      });
    } finally {
      setDeleting(false);
    }
  };

  const deleteStudentFromSession = async (sessionId: string, studentId: string) => {
    try {
      setDeleting(true);
      const { data, error } = await supabase.rpc('delete_student_from_session', {
        session_id_param: sessionId,
        student_id_param: studentId
      });

      if (error) throw error;

      if (!data.success) {
        throw new Error(data.error || 'Failed to remove student from session');
      }

      toast({
        title: t('common.success'),
        description: `Student removed from session successfully. ${data.deleted_credentials} biometric credentials removed.`
      });

      setDeleteStudentData(null);
      loadSessions();

      // Update current session if it's open
      if (currentSession && currentSession.id === sessionId) {
        // Reload the session data to get updated student list
        const { data: sessionData, error: sessionError } = await supabase.rpc('get_registration_session', {
          session_id_param: sessionId
        });

        if (!sessionError && sessionData.success) {
          const updatedSession = {
            id: sessionData.session.id,
            sessionName: sessionData.session.session_name,
            totalStudents: sessionData.session.total_students,
            registeredStudents: sessionData.session.registered_students,
            failedRegistrations: sessionData.session.failed_registrations,
            sessionStatus: sessionData.session.session_status,
            deviceId: sessionData.session.device_id,
            deviceName: sessionData.session.device_name,
            startedAt: sessionData.session.created_at,
            students: (sessionData.students || []).map((s: any) => ({
              id: s.student_id,
              email: s.student_email,
              name: s.student_name,
              status: s.registration_status,
              error: s.error_message,
              registeredAt: s.registered_at,
              order: s.registration_order
            })).sort((a: any, b: any) => a.order - b.order)
          };
          setCurrentSession(updatedSession);
        }
      }
    } catch (error) {
      console.error('Error removing student:', error);
      toast({
        title: t('common.error'),
        description: error instanceof Error ? error.message : 'Failed to remove student',
        variant: 'destructive'
      });
    } finally {
      setDeleting(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'registered':
        return <CheckCircle className="w-4 h-4 text-green-600" />;
      case 'failed':
        return <XCircle className="w-4 h-4 text-red-600" />;
      case 'skipped':
        return <AlertTriangle className="w-4 h-4 text-yellow-600" />;
      default:
        return <Clock className="w-4 h-4 text-gray-400" />;
    }
  };

  const getStatusBadge = (status: string) => {
    const variants = {
      'registered': 'default',
      'failed': 'destructive',
      'skipped': 'secondary',
      'pending': 'outline'
    } as const;

    const statusTranslations = {
      'registered': t("tablet.biometricRegistration.bulkRegistration.registrationSessions.registered"),
      'failed': t("tablet.biometricRegistration.bulkRegistration.registrationSessions.failed"),
      'skipped': t("tablet.biometricRegistration.bulkRegistration.registrationSessions.skipped"),
      'pending': t("tablet.biometricRegistration.bulkRegistration.registrationSessions.pending"),
      'completed': t("tablet.biometricRegistration.bulkRegistration.registrationSessions.completed")
    };

    return (
      <Badge variant={variants[status as keyof typeof variants] || 'outline'}>
        {statusTranslations[status as keyof typeof statusTranslations] || status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    );
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">{t("tablet.biometricRegistration.bulkRegistration.title")}</h2>
          <p className="text-muted-foreground">
            {t("tablet.biometricRegistration.bulkRegistration.subtitle")}
          </p>
        </div>
        
        <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="w-4 h-4 mr-2" />
              {t("tablet.biometricRegistration.bulkRegistration.newSession")}
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[600px]">
            <DialogHeader>
              <DialogTitle>{t("tablet.biometricRegistration.bulkRegistration.createSession.title")}</DialogTitle>
              <DialogDescription>
                {t("tablet.biometricRegistration.bulkRegistration.createSession.description")}
              </DialogDescription>
            </DialogHeader>
            
            <div className="space-y-4">
              <div>
                <Label htmlFor="sessionName">{t("tablet.biometricRegistration.bulkRegistration.createSession.sessionName")}</Label>
                <Input
                  id="sessionName"
                  value={sessionName}
                  onChange={(e) => setSessionName(e.target.value)}
                  placeholder={t("tablet.biometricRegistration.bulkRegistration.createSession.sessionNamePlaceholder")}
                />
              </div>

              <div>
                <Label htmlFor="deviceInfo">{t("tablet.biometricRegistration.bulkRegistration.createSession.deviceInfo")}</Label>
                <div className="p-3 bg-muted rounded-md text-sm">
                  <div className="flex items-center gap-2 mb-1">
                    <Monitor className="w-4 h-4" />
                    <span className="font-medium">{deviceInfo?.deviceName}</span>
                  </div>
                  <div className="text-muted-foreground">
                    {t("tablet.biometricRegistration.bulkRegistration.createSession.deviceId")}: {deviceInfo?.deviceId}
                  </div>
                </div>
              </div>
              
              <div>
                <Label htmlFor="studentEmails">{t("tablet.biometricRegistration.bulkRegistration.createSession.studentEmails")}</Label>
                <Textarea
                  id="studentEmails"
                  value={studentEmails}
                  onChange={(e) => setStudentEmails(e.target.value)}
                  placeholder={t("tablet.biometricRegistration.bulkRegistration.createSession.studentEmailsPlaceholder")}
                  rows={8}
                  className="font-mono text-sm"
                />
                <p className="text-sm text-muted-foreground mt-1">
                  {t("tablet.biometricRegistration.bulkRegistration.createSession.studentEmailsHelp")}
                </p>
              </div>
            </div>
            
            <DialogFooter>
              <Button variant="outline" onClick={() => setShowCreateDialog(false)}>
                {t("tablet.biometricRegistration.bulkRegistration.createSession.cancel")}
              </Button>
              <Button onClick={createSession} disabled={loading}>
                {loading ? t("tablet.biometricRegistration.bulkRegistration.createSession.creating") : t("tablet.biometricRegistration.bulkRegistration.createSession.create")}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      {/* Device Info Card */}
      {deviceInfo && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Monitor className="w-5 h-5" />
              {t("tablet.biometricRegistration.bulkRegistration.currentDevice.title")}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 text-sm">
              <div className="break-words">
                <div className="font-medium text-gray-700 mb-1">{t("tablet.biometricRegistration.bulkRegistration.currentDevice.deviceName")}</div>
                <div className="text-gray-900">{deviceInfo.deviceName}</div>
              </div>
              <div className="break-words">
                <div className="font-medium text-gray-700 mb-1">{t("tablet.biometricRegistration.bulkRegistration.currentDevice.deviceId")}</div>
                <div className="text-gray-900 font-mono text-xs sm:text-sm break-all">{deviceInfo.deviceId}</div>
              </div>
              <div className="break-words">
                <div className="font-medium text-gray-700 mb-1">{t("tablet.biometricRegistration.bulkRegistration.currentDevice.platform")}</div>
                <div className="text-gray-900">{deviceInfo.platform}</div>
              </div>
              <div className="break-words">
                <div className="font-medium text-gray-700 mb-1">{t("tablet.biometricRegistration.bulkRegistration.currentDevice.browser")}</div>
                <div className="text-gray-900">{deviceInfo.userAgent.split(' ')[0]}</div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Registration Sessions */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <FileText className="w-5 h-5" />
                {t("tablet.biometricRegistration.bulkRegistration.registrationSessions.title")}
              </CardTitle>
              <CardDescription>
                {t("tablet.biometricRegistration.bulkRegistration.registrationSessions.description")}
              </CardDescription>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={loadSessions}
              disabled={loading}
            >
              <RotateCcw className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
              {t("tablet.biometricRegistration.bulkRegistration.registrationSessions.refresh")}
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {sessions.length === 0 ? (
            <div className="text-center py-8">
              <Users className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-medium mb-2">{t("tablet.biometricRegistration.bulkRegistration.noSessions.title")}</h3>
              <p className="text-muted-foreground mb-4">
                {t("tablet.biometricRegistration.bulkRegistration.noSessions.description")}
              </p>
              <Button onClick={() => setShowCreateDialog(true)}>
                <Plus className="w-4 h-4 mr-2" />
                {t("tablet.biometricRegistration.bulkRegistration.noSessions.createButton")}
              </Button>
            </div>
          ) : (
            <div className="space-y-4">
              {sessions.map((session) => (
                <Card key={session.id} className="border-l-4 border-l-blue-500">
                  <CardHeader className="pb-3">
                    <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
                      <div className="flex-1 min-w-0">
                        <CardTitle className="text-lg truncate">{session.sessionName}</CardTitle>
                        <CardDescription className="text-sm">
                          Device: {session.deviceName} • Started: {new Date(session.startedAt).toLocaleString()}
                        </CardDescription>
                      </div>
                      <div className="flex items-center gap-2 flex-shrink-0">
                        {getStatusBadge(session.sessionStatus)}
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setCurrentSession(session)}
                          className="hidden sm:inline-flex"
                        >
                          {t("tablet.biometricRegistration.bulkRegistration.registrationSessions.viewDetails")}
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setCurrentSession(session)}
                          className="sm:hidden"
                        >
                          View
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setDeleteSessionId(session.id)}
                          className="text-red-600 hover:text-red-700 hover:bg-red-50"
                          title={t("tablet.biometricRegistration.bulkRegistration.registrationSessions.deleteSession")}
                        >
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-3 gap-4 mb-4">
                      <div className="text-center">
                        <div className="text-2xl font-bold text-blue-600">{session.totalStudents}</div>
                        <div className="text-sm text-muted-foreground">{t("tablet.biometricRegistration.bulkRegistration.registrationSessions.totalStudents")}</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-green-600">{session.registeredStudents}</div>
                        <div className="text-sm text-muted-foreground">{t("tablet.biometricRegistration.bulkRegistration.registrationSessions.registered")}</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-red-600">{session.failedRegistrations}</div>
                        <div className="text-sm text-muted-foreground">{t("tablet.biometricRegistration.bulkRegistration.registrationSessions.failed")}</div>
                      </div>
                    </div>

                    <Progress
                      value={(session.registeredStudents / session.totalStudents) * 100}
                      className="h-2"
                    />
                    <div className="text-sm text-muted-foreground mt-1">
                      {t("tablet.biometricRegistration.bulkRegistration.registrationSessions.progressText", {
                        registered: session.registeredStudents,
                        total: session.totalStudents,
                        percentage: Math.round((session.registeredStudents / session.totalStudents) * 100)
                      })}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Session Details Dialog */}
      {currentSession && (
        <Dialog open={!!currentSession} onOpenChange={() => setCurrentSession(null)}>
          <DialogContent className="sm:max-w-[800px] max-h-[80vh] overflow-y-auto">
            <DialogHeader>
              <div className="flex items-center justify-between">
                <div>
                  <DialogTitle>{currentSession.sessionName}</DialogTitle>
                  <DialogDescription>
                    {t("tablet.biometricRegistration.bulkRegistration.registrationSessions.sessionDetailsTitle")}
                  </DialogDescription>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    setCurrentSession(null);
                    setDeleteSessionId(currentSession.id);
                  }}
                  className="text-red-600 hover:text-red-700 hover:bg-red-50"
                >
                  <Trash2 className="w-4 h-4 mr-2" />
                  {t("tablet.biometricRegistration.bulkRegistration.registrationSessions.deleteSession")}
                </Button>
              </div>
            </DialogHeader>

            <div className="space-y-4">
              {/* Session Summary */}
              <div className="grid grid-cols-4 gap-4 p-4 bg-muted rounded-lg">
                <div className="text-center">
                  <div className="text-lg font-bold">{currentSession.totalStudents}</div>
                  <div className="text-sm text-muted-foreground">{t("tablet.biometricRegistration.bulkRegistration.registrationSessions.total")}</div>
                </div>
                <div className="text-center">
                  <div className="text-lg font-bold text-green-600">{currentSession.registeredStudents}</div>
                  <div className="text-sm text-muted-foreground">{t("tablet.biometricRegistration.bulkRegistration.registrationSessions.registered")}</div>
                </div>
                <div className="text-center">
                  <div className="text-lg font-bold text-red-600">{currentSession.failedRegistrations}</div>
                  <div className="text-sm text-muted-foreground">{t("tablet.biometricRegistration.bulkRegistration.registrationSessions.failed")}</div>
                </div>
                <div className="text-center">
                  <div className="text-lg font-bold text-yellow-600">
                    {currentSession.totalStudents - currentSession.registeredStudents - currentSession.failedRegistrations}
                  </div>
                  <div className="text-sm text-muted-foreground">{t("tablet.biometricRegistration.bulkRegistration.registrationSessions.pending")}</div>
                </div>
              </div>

              {/* Students Table */}
              <div className="border rounded-lg">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>{t("tablet.biometricRegistration.bulkRegistration.registrationSessions.tableHeaders.student")}</TableHead>
                      <TableHead>{t("tablet.biometricRegistration.bulkRegistration.registrationSessions.tableHeaders.email")}</TableHead>
                      <TableHead>{t("tablet.biometricRegistration.bulkRegistration.registrationSessions.tableHeaders.status")}</TableHead>
                      <TableHead>{t("tablet.biometricRegistration.bulkRegistration.registrationSessions.tableHeaders.registeredAt")}</TableHead>
                      <TableHead>{t("tablet.biometricRegistration.bulkRegistration.registrationSessions.tableHeaders.notes")}</TableHead>
                      <TableHead>{t("tablet.biometricRegistration.bulkRegistration.registrationSessions.tableHeaders.actions")}</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {currentSession.students.map((student) => (
                      <TableRow key={student.id}>
                        <TableCell className="font-medium">{student.name}</TableCell>
                        <TableCell>{student.email}</TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            {getStatusIcon(student.status)}
                            {getStatusBadge(student.status)}
                          </div>
                        </TableCell>
                        <TableCell>
                          {student.registeredAt
                            ? new Date(student.registeredAt).toLocaleString()
                            : '-'
                          }
                        </TableCell>
                        <TableCell>
                          {student.error && (
                            <span className="text-sm text-red-600">{student.error}</span>
                          )}
                        </TableCell>
                        <TableCell>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => setDeleteStudentData({
                              sessionId: currentSession.id,
                              studentId: student.id,
                              studentName: student.name
                            })}
                            className="text-red-600 hover:text-red-700 hover:bg-red-50"
                            title={`Remove ${student.name} from session`}
                          >
                            <UserX className="w-4 h-4" />
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </div>

            <DialogFooter>
              <Button variant="outline" onClick={() => setCurrentSession(null)}>
                {t("common.close")}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}

      {/* Delete Session Confirmation Dialog */}
      <Dialog open={!!deleteSessionId} onOpenChange={() => setDeleteSessionId(null)}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{t("tablet.biometricRegistration.bulkRegistration.registrationSessions.deleteConfirmation.title")}</DialogTitle>
            <DialogDescription asChild>
              <div>
                <p>{t("tablet.biometricRegistration.bulkRegistration.registrationSessions.deleteConfirmation.description")}</p>
                <ul className="list-disc list-inside mt-2 space-y-1">
                  <li>{t("tablet.biometricRegistration.bulkRegistration.registrationSessions.deleteConfirmation.consequences.removeStudents")}</li>
                  <li>{t("tablet.biometricRegistration.bulkRegistration.registrationSessions.deleteConfirmation.consequences.deleteCredentials")}</li>
                  <li>{t("tablet.biometricRegistration.bulkRegistration.registrationSessions.deleteConfirmation.consequences.resetStatus")}</li>
                </ul>
                <strong className="text-red-600 block mt-2">{t("tablet.biometricRegistration.bulkRegistration.registrationSessions.deleteConfirmation.warning")}</strong>
              </div>
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setDeleteSessionId(null)}>
              {t("tablet.biometricRegistration.bulkRegistration.createSession.cancel")}
            </Button>
            <Button
              variant="destructive"
              onClick={() => deleteSessionId && deleteSession(deleteSessionId)}
              disabled={deleting}
            >
              {deleting ? t("tablet.biometricRegistration.bulkRegistration.registrationSessions.deleteConfirmation.deleting") : t("tablet.biometricRegistration.bulkRegistration.registrationSessions.deleteConfirmation.deleteButton")}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Student Confirmation Dialog */}
      <Dialog open={!!deleteStudentData} onOpenChange={() => setDeleteStudentData(null)}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Remove Student from Session</DialogTitle>
            <DialogDescription asChild>
              <div>
                <p>Are you sure you want to remove <strong>{deleteStudentData?.studentName}</strong> from this registration session? This will:</p>
                <ul className="list-disc list-inside mt-2 space-y-1">
                  <li>Remove the student from the session</li>
                  <li>Delete their biometric credentials from this device</li>
                  <li>Reset their biometric status if no other credentials exist</li>
                </ul>
                <strong className="text-red-600 block mt-2">This action cannot be undone.</strong>
              </div>
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setDeleteStudentData(null)}>
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={() => deleteStudentData && deleteStudentFromSession(deleteStudentData.sessionId, deleteStudentData.studentId)}
              disabled={deleting}
            >
              {deleting ? 'Removing...' : 'Remove Student'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
