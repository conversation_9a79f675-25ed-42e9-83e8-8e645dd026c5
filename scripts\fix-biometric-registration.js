// This script fixes the biometric registration validation issue
// It applies the updated register_student_in_session function

import fs from 'fs';
import path from 'path';
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

dotenv.config();

// Create Supabase client
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Error: Supabase URL or service key not found in environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function fixBiometricRegistration() {
  try {
    console.log('🔧 Starting biometric registration fix...');
    
    // Read the migration file
    const migrationPath = path.join(__dirname, '../supabase/migrations/20250116000000_fix_student_registration_validation.sql');
    const migrationSql = fs.readFileSync(migrationPath, 'utf8');
    
    console.log('📄 Migration file loaded successfully');
    console.log('🔄 Executing SQL migration...');
    
    // Split the SQL into statements and execute them one by one
    const statements = migrationSql
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));
    
    console.log(`📝 Found ${statements.length} SQL statements to execute`);
    
    for (let i = 0; i < statements.length; i++) {
      console.log(`⚡ Executing statement ${i + 1} of ${statements.length}...`);
      
      try {
        // Use the database query endpoint directly
        const response = await fetch(`${supabaseUrl}/rest/v1/rpc/exec_sql`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'apikey': supabaseServiceKey,
            'Authorization': `Bearer ${supabaseServiceKey}`
          },
          body: JSON.stringify({ sql: statements[i] + ';' })
        });
        
        if (!response.ok) {
          const errorData = await response.text();
          console.error(`❌ Error executing statement ${i + 1}:`, errorData);
          
          // Try with RPC function as fallback
          console.log(`🔄 Trying RPC fallback for statement ${i + 1}...`);
          const { data: rpcData, error: rpcError } = await supabase.rpc('exec_sql', { 
            sql: statements[i] + ';' 
          });
          
          if (rpcError) {
            console.error(`❌ RPC fallback also failed for statement ${i + 1}:`, rpcError);
          } else {
            console.log(`✅ Statement ${i + 1} executed successfully via RPC`);
          }
        } else {
          console.log(`✅ Statement ${i + 1} executed successfully`);
        }
      } catch (stmtErr) {
        console.error(`❌ Error executing statement ${i + 1}:`, stmtErr);
      }
    }
    
    // Verify the function was updated
    console.log('🔍 Verifying function update...');
    
    const { data: functionInfo, error: functionError } = await supabase
      .from('information_schema.routines')
      .select('routine_name, routine_definition')
      .eq('routine_name', 'register_student_in_session')
      .eq('routine_schema', 'public');
    
    if (functionError) {
      console.error('❌ Error checking function:', functionError);
    } else if (functionInfo && functionInfo.length > 0) {
      console.log('✅ Function register_student_in_session found and updated');
      
      // Check if the new validation logic is present
      const definition = functionInfo[0].routine_definition || '';
      if (definition.includes('registration_session_students')) {
        console.log('✅ New validation logic detected in function');
      } else {
        console.log('⚠️  Warning: New validation logic not detected in function definition');
      }
    } else {
      console.log('❌ Function register_student_in_session not found');
    }
    
    console.log('🎉 Biometric registration fix completed!');
    console.log('');
    console.log('📋 Summary of changes:');
    console.log('   • Updated register_student_in_session function');
    console.log('   • Changed validation to check session membership instead of school_id match');
    console.log('   • Added additional verification for student role');
    console.log('   • This should resolve the "Student not found in school" error');
    console.log('');
    console.log('🔄 Please test the biometric registration process again.');
    
  } catch (error) {
    console.error('❌ Error applying biometric registration fix:', error);
    process.exit(1);
  }
}

// Execute the fix
fixBiometricRegistration();
