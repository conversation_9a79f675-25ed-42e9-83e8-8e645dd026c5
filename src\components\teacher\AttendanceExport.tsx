import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { format as formatDate, subDays, format, startOfDay, endOfDay } from "date-fns";
import { tr, enUS } from "date-fns/locale";
import jsPDF from "jspdf";
import html2canvas from "html2canvas";
import {
  Calendar as CalendarIcon,
  Download,
  FileDown,
  AlertCircle,
  Check,
  Filter,
  ChevronDown,
  Globe,
  FileImage,
  FileSpreadsheet,
} from "lucide-react";
import { cn } from "@/lib/utils";
import {
  filterRecordsByStatus,
} from "@/lib/reportUtils";
import { AttendanceRecord } from "@/lib/types";
import { supabase } from "@/lib/supabase";
import { useAuth } from "@/context/AuthContext";
import { useToast } from "@/hooks/use-toast";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { BRANDING, getBranding } from "@/config/branding";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Badge } from "@/components/ui/badge";
import { useTranslation } from "react-i18next";

interface AttendanceExportProps {
  roomId?: string;
  roomName?: string;
}

interface EnhancedAttendanceRecord extends AttendanceRecord {
  studentName: string;
  roomName: string;
}

// Define status type for filtering
type StatusFilter = "all" | "present" | "absent" | "late" | "excused";

export function AttendanceExport({ roomId, roomName }: AttendanceExportProps) {
  const { t, i18n } = useTranslation();
  const { profile } = useAuth();

  // Get the appropriate locale for date formatting
  const getDateLocale = () => {
    return i18n.language === 'tr' ? tr : enUS;
  };

  // Status display information with translations
  const statusInfo = {
    all: {
      label: t("attendance.status.allStatuses"),
      color: "default",
      icon: "🔍",
    },
    present: {
      label: t("attendance.status.presentOnly"),
      color: "green",
      icon: "✅",
    },
    absent: {
      label: t("attendance.status.absentOnly"),
      color: "destructive",
      icon: "❌",
    },
    late: {
      label: t("attendance.status.lateOnly"),
      color: "yellow",
      icon: "⏰",
    },
    excused: {
      label: t("attendance.status.excusedOnly"),
      color: "blue",
      icon: "📝",
    },
  };

  // Define date period type
  type DatePeriod = "today" | "yesterday";

  const [selectedPeriod, setSelectedPeriod] = useState<DatePeriod>("today");
  const [records, setRecords] = useState<EnhancedAttendanceRecord[]>([]);
  const [loading, setLoading] = useState(false);
  const [recordsCount, setRecordsCount] = useState<number>(0);
  const [selectedStatus, setSelectedStatus] = useState<StatusFilter>("all");
  const { toast } = useToast();

  // Helper function to get date range based on selected period
  const getDateRangeForPeriod = (period: DatePeriod) => {
    if (period === "today") {
      const today = new Date();
      return {
        from: startOfDay(today),
        to: endOfDay(today)
      };
    } else { // yesterday
      const yesterday = subDays(new Date(), 1);
      return {
        from: startOfDay(yesterday),
        to: endOfDay(yesterday)
      };
    }
  };

  // Get attendance records for the selected date (similar to admin approach)
  const getAttendanceRecords = async (selectedDate: Date): Promise<EnhancedAttendanceRecord[]> => {
    if (!profile?.school_id || !roomId) return [];

    try {
      const dayStart = startOfDay(selectedDate);
      const dayEnd = endOfDay(selectedDate);

      // Get all students in the room
      const { data: allStudents, error: studentsError } = await supabase
        .from("profiles")
        .select("id, name, student_id, room_id")
        .eq("role", "student")
        .eq("school_id", profile.school_id)
        .eq("room_id", roomId);

      if (studentsError) throw studentsError;

      // Get attendance records for the selected date
      const { data: attendanceData, error: attendanceError } = await supabase
        .from("attendance_records")
        .select(`
          id,
          student_id,
          room_id,
          timestamp,
          device_info,
          location,
          verification_method,
          status,
          created_at
        `)
        .eq("school_id", profile.school_id)
        .eq("room_id", roomId)
        .gte("timestamp", dayStart.toISOString())
        .lte("timestamp", dayEnd.toISOString())
        .order("timestamp", { ascending: true });

      if (attendanceError) throw attendanceError;

      // Get room information
      const { data: roomData, error: roomError } = await supabase
        .from("rooms")
        .select("id, name")
        .eq("id", roomId)
        .single();

      if (roomError) throw roomError;

      // Create enhanced records for all students (including absent ones)
      const enhancedRecords: EnhancedAttendanceRecord[] = (allStudents || []).map((student) => {
        const attendanceRecord = attendanceData?.find(record => record.student_id === student.id);

        if (attendanceRecord) {
          // Student has an attendance record
          return {
            id: attendanceRecord.id,
            studentId: attendanceRecord.student_id,
            studentName: student.name || "Unknown Student",
            roomId: attendanceRecord.room_id,
            roomName: roomData?.name || roomName || "Unknown Room",
            timestamp: attendanceRecord.timestamp,
            status: attendanceRecord.status as "present" | "absent" | "late" | "excused",
            verificationMethod: attendanceRecord.verification_method as "manual" | "qr" | "nfc",
            deviceInfo: attendanceRecord.device_info,
            location: attendanceRecord.location,
            createdAt: attendanceRecord.created_at,
          };
        } else {
          // Student is absent (no attendance record)
          return {
            id: `absent-${student.id}-${format(selectedDate, 'yyyy-MM-dd')}`,
            studentId: student.student_id || student.id,
            studentName: student.name || "Unknown Student",
            roomId: student.room_id || "",
            roomName: roomData?.name || roomName || "Unknown Room",
            timestamp: dayStart.toISOString(),
            status: "absent" as const,
            verificationMethod: "manual" as const,
            deviceInfo: "No attendance record",
            location: null,
            createdAt: dayStart.toISOString(),
          };
        }
      });

      return enhancedRecords;
    } catch (error) {
      console.error("Error fetching attendance records:", error);
      return [];
    }
  };



  // Fetch records when period changes
  useEffect(() => {
    const fetchRecords = async () => {
      if (!profile?.school_id || !roomId) return;

      setLoading(true);
      try {
        const selectedDate = selectedPeriod === "today" ? new Date() : subDays(new Date(), 1);
        const fetchedRecords = await getAttendanceRecords(selectedDate);
        setRecords(fetchedRecords);
      } catch (error) {
        console.error("Error fetching records:", error);
        toast({
          title: t("common.error"),
          description: t("attendance.export.fetchError"),
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    };

    fetchRecords();
  }, [selectedPeriod, profile?.school_id, roomId]);

  // Update records count when records or status filter changes
  useEffect(() => {
    if (records.length > 0) {
      // Filter by status if not "all"
      let filteredRecords = records;
      if (selectedStatus !== "all") {
        filteredRecords = filterRecordsByStatus(records, selectedStatus);
      }

      setRecordsCount(filteredRecords.length);
    } else {
      setRecordsCount(0);
    }
  }, [records, selectedStatus]);

  const handleExport = (format: "csv" | "pdf") => {
    console.log("Exporting records:", {
      totalRecords: records.length,
      period: selectedPeriod,
      statusFilter: selectedStatus,
    });

    // Get filtered records (already filtered by date from database)
    const filteredRecords = getFilteredRecords();

    if (filteredRecords.length === 0) {
      toast({
        title: t("attendance.export.noRecordsFound"),
        description:
          selectedStatus === "all"
            ? t("attendance.export.noRecordsForDateRange")
            : t("attendance.export.noStatusRecordsForDateRange", {
                status: t(`attendance.status.${selectedStatus}`),
              }),
        variant: "destructive",
      });
      return;
    }

    // Add room name to each record for better reporting
    const enhancedRecords = filteredRecords.map((record) => ({
      ...record,
      roomName: record.roomName || roomName || "Unknown Room",
    }));

    const dateStr = formatDate(new Date(), "yyyy-MM-dd");

    // Include status in the title if filtering
    const statusText =
      selectedStatus !== "all" ? ` (${statusInfo[selectedStatus].label})` : "";

    const title = `Attendance Report - ${roomName || "All Rooms"}${statusText}`;

    // Include status in the filename if filtering
    const statusSuffix = selectedStatus !== "all" ? `-${selectedStatus}` : "";

    const filename = `attendance-report-${
      roomName ? roomName.replace(/\s+/g, "-").toLowerCase() : "all"
    }${statusSuffix}-${dateStr}`;

    try {
      if (format === "csv") {
        downloadCSV(enhancedRecords, `${filename}.csv`, dateRange, t);
      } else {
        downloadPDF(enhancedRecords, title, `${filename}.pdf`, dateRange, t);
      }

      toast({
        title: t("attendance.export.exportSuccessful"),
        description: t("attendance.export.reportExportedAs", {
          format: format.toUpperCase(),
        }),
      });
    } catch (error) {
      console.error("Export error:", error);
      toast({
        title: t("attendance.export.exportFailed"),
        description: t("attendance.export.failedToGenerate"),
        variant: "destructive",
      });
    }
  };

  // Helper function to get filtered records
  const getFilteredRecords = () => {
    // Debug: Log the filtering process
    console.log(`Filtering ${selectedPeriod} records:`, {
      period: selectedPeriod,
      totalRecords: records.length,
      statusFilter: selectedStatus
    });

    // Filter by status if not "all" (records are already filtered by date from database)
    let filteredRecords = records;
    if (selectedStatus !== "all") {
      filteredRecords = filterRecordsByStatus(records, selectedStatus);
    }

    console.log(`Filtered ${selectedPeriod} records:`, {
      period: selectedPeriod,
      statusFilter: selectedStatus,
      filteredCount: filteredRecords.length,
      originalCount: records.length
    });

    // Records already have room names from the database query
    return filteredRecords;
  };

  // Generate beautiful HTML content
  const generateHTMLContent = (forPrint = false, filteredRecords: AttendanceRecord[]) => {
    const locale = t("common.locale") === "tr" ? tr : enUS;
    const currentDate = format(new Date(), "d MMMM yyyy", { locale });
    const totalRecords = filteredRecords.length;

    // Calculate statistics
    const presentCount = filteredRecords.filter(r => r.status === "present").length;
    const absentCount = filteredRecords.filter(r => r.status === "absent").length;
    const lateCount = filteredRecords.filter(r => r.status === "late").length;
    const excusedCount = filteredRecords.filter(r => r.status === "excused").length;

    // Get date range string
    const selectedDate = selectedPeriod === "today" ? new Date() : subDays(new Date(), 1);
    const dateRangeStr = format(selectedDate, "d MMM yyyy", { locale });

    return `<!DOCTYPE html>
<html lang="${t("common.locale")}" dir="${t("common.direction")}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${t("attendance.export.attendanceReport")}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', system-ui, -apple-system, sans-serif;
            line-height: 1.6;
            color: #2d3748;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            background-attachment: fixed;
            min-height: 100vh;
            padding: 20px;
            position: relative;
            overflow-x: hidden;
        }

        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%);
            pointer-events: none;
            z-index: 0;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 24px;
            box-shadow:
                0 32px 64px rgba(0, 0, 0, 0.12),
                0 0 0 1px rgba(255, 255, 255, 0.2);
            overflow: hidden;
            position: relative;
            z-index: 1;
            ${forPrint ? 'box-shadow: none; border-radius: 0; background: white; backdrop-filter: none;' : ''}
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            color: white;
            padding: 32px 40px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background:
                radial-gradient(circle, rgba(255,255,255,0.15) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255,255,255,0.1) 0%, transparent 60%);
            animation: shimmer 4s ease-in-out infinite;
        }

        @keyframes shimmer {
            0%, 100% {
                transform: rotate(0deg) scale(1);
                opacity: 0.8;
            }
            50% {
                transform: rotate(180deg) scale(1.1);
                opacity: 1;
            }
        }

        .header h1 {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 5px;
            position: relative;
            z-index: 1;
        }

        .header h2 {
            font-size: 1rem;
            font-weight: 400;
            opacity: 0.9;
            position: relative;
            z-index: 1;
            margin-bottom: 5px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
            gap: 20px;
            padding: 32px 40px;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            position: relative;
        }

        .stats-grid::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(circle at 10% 20%, rgba(120, 119, 198, 0.05) 0%, transparent 50%),
                radial-gradient(circle at 90% 80%, rgba(255, 119, 198, 0.05) 0%, transparent 50%);
            pointer-events: none;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            padding: 20px;
            border-radius: 16px;
            text-align: center;
            box-shadow:
                0 8px 32px rgba(0, 0, 0, 0.08),
                0 0 0 1px rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            transition: all 0.3s ease;
            position: relative;
            z-index: 1;
        }

        .stat-card:hover {
            transform: translateY(-4px) scale(1.02);
            box-shadow:
                0 16px 48px rgba(0, 0, 0, 0.12),
                0 0 0 1px rgba(255, 255, 255, 0.3);
        }

        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 4px;
        }

        .stat-label {
            font-size: 0.8rem;
            color: #64748b;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .stat-card.total .stat-number { color: #3b82f6; }
        .stat-card.present .stat-number { color: #10b981; }
        .stat-card.absent .stat-number { color: #ef4444; }
        .stat-card.late .stat-number { color: #f59e0b; }
        .stat-card.excused .stat-number { color: #8b5cf6; }

        .content {
            padding: 25px 40px;
        }

        .table-container {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            border: 1px solid #e2e8f0;
        }

        .table-wrapper {
            overflow-x: auto;
            -webkit-overflow-scrolling: touch;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            min-width: 600px;
        }

        th {
            background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
            color: white;
            padding: 16px 20px;
            text-align: left;
            font-weight: 600;
            font-size: 0.85rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            border: none;
            position: relative;
        }

        th::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg, transparent 0%, rgba(255,255,255,0.1) 50%, transparent 100%);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        th:hover::before {
            opacity: 1;
        }

        td {
            padding: 10px 15px;
            border-bottom: 1px solid #f1f5f9;
            vertical-align: middle;
        }

        tr:nth-child(even) {
            background: #f8fafc;
        }

        tr:hover {
            background: #e2e8f0;
        }

        .status-badge {
            display: inline-flex;
            align-items: center;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .status-present {
            background: #dcfce7;
            color: #166534;
            border: 1px solid #bbf7d0;
        }

        .status-absent {
            background: #fef2f2;
            color: #991b1b;
            border: 1px solid #fecaca;
        }

        .status-late {
            background: #fefbeb;
            color: #92400e;
            border: 1px solid #fed7aa;
        }

        .status-excused {
            background: #f3e8ff;
            color: #6b21a8;
            border: 1px solid #d8b4fe;
        }

        .footer {
            background: #f8fafc;
            padding: 30px 40px;
            text-align: center;
            border-top: 1px solid #e2e8f0;
            color: #64748b;
        }

        .footer p {
            margin: 5px 0;
        }

        .export-info {
            font-size: 0.9rem;
            opacity: 0.8;
        }

        @media print {
            body {
                background: white;
                padding: 0;
            }

            .container {
                box-shadow: none;
                border-radius: 0;
            }

            .stat-card:hover {
                transform: none;
            }
        }

        @media (max-width: 768px) {
            .header {
                padding: 15px 20px;
            }

            .header h1 {
                font-size: 1.5rem;
            }

            .header h2 {
                font-size: 0.9rem;
            }

            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
                padding: 15px 20px;
                gap: 10px;
            }

            .stat-card {
                padding: 10px;
            }

            .content {
                padding: 15px 20px;
            }

            .table-container {
                border-radius: 10px;
                margin: 0 -10px;
            }

            .table-wrapper {
                overflow-x: auto;
                -webkit-overflow-scrolling: touch;
                scrollbar-width: thin;
                scrollbar-color: #cbd5e0 #f7fafc;
            }

            .table-wrapper::-webkit-scrollbar {
                height: 6px;
            }

            .table-wrapper::-webkit-scrollbar-track {
                background: #f7fafc;
            }

            .table-wrapper::-webkit-scrollbar-thumb {
                background: #cbd5e0;
                border-radius: 3px;
            }

            table {
                min-width: 700px;
            }

            th, td {
                padding: 8px 6px;
                font-size: 0.75rem;
                white-space: nowrap;
            }

            th {
                font-size: 0.7rem;
                padding: 12px 8px;
            }

            .stat-number {
                font-size: 1.5rem;
            }

            .stat-label {
                font-size: 0.7rem;
            }

            .status-badge {
                font-size: 0.65rem;
                padding: 2px 6px;
            }
        }

        @media (max-width: 480px) {
            .header {
                padding: 10px 15px;
            }

            .header h1 {
                font-size: 1.25rem;
            }

            .header h2 {
                font-size: 0.8rem;
            }

            .stats-grid {
                grid-template-columns: 1fr;
                padding: 10px 15px;
                gap: 8px;
            }

            .stat-card {
                padding: 8px;
            }

            .content {
                padding: 10px 15px;
            }

            .table-container {
                margin: 0 -15px;
                border-radius: 8px;
            }

            table {
                min-width: 650px;
            }

            th, td {
                padding: 6px 4px;
                font-size: 0.7rem;
            }

            th {
                font-size: 0.65rem;
                padding: 10px 6px;
            }

            .stat-number {
                font-size: 1.25rem;
            }

            .stat-label {
                font-size: 0.65rem;
            }

            .status-badge {
                font-size: 0.6rem;
                padding: 1px 4px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>${t("attendance.export.attendanceReport")}</h1>
            <h2>${t("attendance.export.comprehensiveReport")}</h2>
            <p style="margin-top: 8px; opacity: 0.9; font-size: 0.9rem;">${t("attendance.export.generatedOn")}: ${dateRangeStr}</p>
        </div>

        <div class="stats-grid">
            <div class="stat-card total">
                <div class="stat-number">${totalRecords}</div>
                <div class="stat-label">${t("attendance.export.totalRecords")}</div>
            </div>
            <div class="stat-card present">
                <div class="stat-number">${presentCount}</div>
                <div class="stat-label">${t("attendance.status.present")}</div>
            </div>
            <div class="stat-card absent">
                <div class="stat-number">${absentCount}</div>
                <div class="stat-label">${t("attendance.status.absent")}</div>
            </div>
            <div class="stat-card late">
                <div class="stat-number">${lateCount}</div>
                <div class="stat-label">${t("attendance.status.late")}</div>
            </div>
            <div class="stat-card excused">
                <div class="stat-number">${excusedCount}</div>
                <div class="stat-label">${t("attendance.status.excused")}</div>
            </div>
        </div>

        <div class="content">
            <div class="table-container">
                <div class="table-wrapper">
                    <table>
                    <thead>
                        <tr>
                            <th>📅 ${t("attendance.export.headers.dateTime")}</th>
                            <th>👤 ${t("attendance.export.headers.studentName")}</th>
                            <th>🆔 ${t("attendance.export.headers.studentId")}</th>
                            <th>🏫 ${t("attendance.export.headers.room")}</th>
                            <th>📊 ${t("attendance.export.headers.status")}</th>
                            <th>🔍 ${t("attendance.export.headers.verification")}</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${filteredRecords.map(record => {
                            // Safely handle date formatting with fallback
                            let formattedDate = t("attendance.export.notAvailable");
                            try {
                                // Try timestamp first, then createdAt as fallback
                                const dateValue = record.timestamp || record.createdAt;
                                if (dateValue) {
                                    const date = new Date(dateValue);
                                    if (!isNaN(date.getTime())) {
                                        formattedDate = format(date, "d MMM yyyy, HH:mm", { locale });
                                    }
                                }
                            } catch (error) {
                                console.warn("Invalid date in record:", record.timestamp || record.createdAt);
                            }

                            return `
                            <tr>
                                <td>
                                    <strong>${formattedDate}</strong>
                                </td>
                                <td><strong>${record.studentName || t("attendance.export.unknownStudent")}</strong></td>
                                <td>${record.studentId || "N/A"}</td>
                                <td>${record.roomName || t("attendance.export.unknownRoom")}</td>
                                <td>
                                    <span class="status-badge status-${record.status}">
                                        ${t(`attendance.status.${record.status}`)}
                                    </span>
                                </td>
                                <td>${record.verificationMethod === "manual"
                                    ? t("attendance.export.manualByTeacher")
                                    : record.verificationMethod
                                      ? record.verificationMethod.charAt(0).toUpperCase() + record.verificationMethod.slice(1)
                                      : t("attendance.export.notAvailable")}</td>
                            </tr>`;
                        }).join('')}
                    </tbody>
                    </table>
                </div>
            </div>
        </div>

        <div class="footer">
            <p><strong>${t("app.name")}</strong></p>
            <p class="export-info">${t("attendance.export.reportContains", { count: totalRecords, date: currentDate })}</p>
        </div>
    </div>
</body>
</html>`;
  };

  // Helper function to download files
  const downloadFile = (content: string, filename: string, mimeType: string) => {
    const blob = new Blob([content], { type: `${mimeType};charset=utf-8;` });
    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.setAttribute("href", url);
    link.setAttribute("download", filename);
    link.style.visibility = "hidden";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  // Helper function to show export success message
  // Generate clean PDF content for mobile-friendly PDF generation
  const generatePDFContent = (filteredRecords: any[]) => {
    const periodText = selectedPeriod === "today"
      ? t("attendance.export.todaysRecords")
      : t("attendance.export.yesterdaysRecords");

    const selectedDate = selectedPeriod === "today" ? new Date() : subDays(new Date(), 1);
    const dateStr = formatDate(selectedDate, "dd/MM/yyyy", { locale: getDateLocale() });

    // Calculate detailed statistics
    const stats = {
      total: filteredRecords.length,
      present: filteredRecords.filter(r => r.status === 'present').length,
      absent: filteredRecords.filter(r => r.status === 'absent').length,
      late: filteredRecords.filter(r => r.status === 'late').length,
      excused: filteredRecords.filter(r => r.status === 'excused').length,
    };

    // Get internationalized branding
    const currentBranding = getBranding(i18n.language);

    return `
      <div style="font-family: Arial, sans-serif; color: #333; line-height: 1.4;">
        <!-- Header -->
        <div style="text-align: center; margin-bottom: 30px; border-bottom: 2px solid #4f46e5; padding-bottom: 20px;">
          <h1 style="color: #4f46e5; margin: 0; font-size: 24px; font-weight: bold;">
            ${currentBranding.APP_NAME}
          </h1>
          <h2 style="color: #666; margin: 10px 0 0 0; font-size: 18px;">
            ${t("attendance.export.attendanceReport")}
          </h2>
          <p style="color: #888; margin: 5px 0 0 0; font-size: 14px;">
            ${periodText} - ${dateStr}
          </p>
        </div>

        <!-- Summary -->
        <div style="margin-bottom: 25px; padding: 15px; background-color: #f8fafc; border-radius: 8px; border-left: 4px solid #4f46e5;">
          <h3 style="margin: 0 0 10px 0; color: #4f46e5; font-size: 16px;">
            ${t("attendance.export.summary")}
          </h3>
          <p style="margin: 0 0 8px 0; font-size: 14px;">
            <strong>${t("attendance.export.totalRecords")}:</strong> ${stats.total}
            ${selectedStatus !== "all" ? ` | <strong>${t("attendance.export.filter")}:</strong> ${statusInfo[selectedStatus].label}` : ""}
          </p>
          <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(100px, 1fr)); gap: 10px; margin-top: 10px; font-size: 13px;">
            <div style="text-align: center; padding: 8px; background-color: white; border-radius: 4px; border: 1px solid #e5e7eb;">
              <div style="font-size: 16px; font-weight: bold; color: #10b981;">${stats.present}</div>
              <div style="color: #6b7280; font-size: 11px;">${t("attendance.status.present")}</div>
            </div>
            <div style="text-align: center; padding: 8px; background-color: white; border-radius: 4px; border: 1px solid #e5e7eb;">
              <div style="font-size: 16px; font-weight: bold; color: #ef4444;">${stats.absent}</div>
              <div style="color: #6b7280; font-size: 11px;">${t("attendance.status.absent")}</div>
            </div>
            <div style="text-align: center; padding: 8px; background-color: white; border-radius: 4px; border: 1px solid #e5e7eb;">
              <div style="font-size: 16px; font-weight: bold; color: #f59e0b;">${stats.late}</div>
              <div style="color: #6b7280; font-size: 11px;">${t("attendance.status.late")}</div>
            </div>
            <div style="text-align: center; padding: 8px; background-color: white; border-radius: 4px; border: 1px solid #e5e7eb;">
              <div style="font-size: 16px; font-weight: bold; color: #3b82f6;">${stats.excused}</div>
              <div style="color: #6b7280; font-size: 11px;">${t("attendance.status.excused")}</div>
            </div>
          </div>
        </div>

        <!-- Table -->
        <table style="width: 100%; border-collapse: collapse; margin-top: 20px; font-size: 12px;">
          <thead>
            <tr style="background-color: #4f46e5; color: white;">
              <th style="padding: 12px 8px; text-align: left; border: 1px solid #ddd; font-weight: bold;">
                ${t("attendance.export.headers.dateTime")}
              </th>
              <th style="padding: 12px 8px; text-align: left; border: 1px solid #ddd; font-weight: bold;">
                ${t("attendance.export.headers.studentName")}
              </th>
              <th style="padding: 12px 8px; text-align: left; border: 1px solid #ddd; font-weight: bold;">
                ${t("attendance.export.headers.studentId")}
              </th>
              <th style="padding: 12px 8px; text-align: left; border: 1px solid #ddd; font-weight: bold;">
                ${t("attendance.export.headers.status")}
              </th>
              <th style="padding: 12px 8px; text-align: left; border: 1px solid #ddd; font-weight: bold;">
                ${t("attendance.export.headers.verification")}
              </th>
            </tr>
          </thead>
          <tbody>
            ${filteredRecords.map((record, index) => {
              // Format date safely
              let formattedDate = t("attendance.export.notAvailable");
              try {
                const dateValue = record.timestamp || record.createdAt;
                if (dateValue) {
                  const date = new Date(dateValue);
                  if (!isNaN(date.getTime())) {
                    formattedDate = formatDate(date, "dd/MM/yyyy HH:mm", { locale: getDateLocale() });
                  }
                }
              } catch (error) {
                console.warn("Date formatting error:", error);
              }

              // Get status color
              const getStatusColor = (status: string) => {
                switch (status) {
                  case 'present': return '#10b981';
                  case 'absent': return '#ef4444';
                  case 'late': return '#f59e0b';
                  case 'excused': return '#3b82f6';
                  default: return '#6b7280';
                }
              };

              return `
                <tr style="background-color: ${index % 2 === 0 ? '#f9fafb' : 'white'};">
                  <td style="padding: 10px 8px; border: 1px solid #e5e7eb; font-size: 11px;">
                    ${formattedDate}
                  </td>
                  <td style="padding: 10px 8px; border: 1px solid #e5e7eb; font-weight: 500;">
                    ${record.studentName || t("attendance.export.unknownStudent")}
                  </td>
                  <td style="padding: 10px 8px; border: 1px solid #e5e7eb; font-family: monospace;">
                    ${record.studentId || "N/A"}
                  </td>
                  <td style="padding: 10px 8px; border: 1px solid #e5e7eb;">
                    <span style="color: ${getStatusColor(record.status)}; font-weight: bold;">
                      ${t(`attendance.status.${record.status}`)}
                    </span>
                  </td>
                  <td style="padding: 10px 8px; border: 1px solid #e5e7eb; font-size: 11px;">
                    ${record.verificationMethod === "manual"
                      ? t("attendance.export.manualByTeacher")
                      : record.verificationMethod
                        ? record.verificationMethod.charAt(0).toUpperCase() + record.verificationMethod.slice(1)
                        : t("attendance.export.notAvailable")
                    }
                  </td>
                </tr>
              `;
            }).join('')}
          </tbody>
        </table>

        <!-- Footer -->
        <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #e5e7eb; text-align: center; color: #6b7280; font-size: 11px;">
          <p style="margin: 0;">
            ${t("attendance.export.generatedOn")} ${formatDate(new Date(), "dd/MM/yyyy HH:mm", { locale: getDateLocale() })}
          </p>
          <p style="margin: 5px 0 0 0;">
            ${currentBranding.APP_NAME} - ${t("attendance.export.attendanceManagementSystem")}
          </p>
        </div>
      </div>
    `;
  };

  const showExportSuccess = (format: string) => {
    toast({
      title: t("attendance.export.exportComplete"),
      description: t("attendance.export.exportSuccess", { count: getFilteredRecords().length, format }),
    });
  };

  // Export attendance to CSV
  const exportToCSV = () => {
    const filteredRecords = getFilteredRecords();

    // Debug: Log the first record to see its structure
    if (filteredRecords.length > 0) {
      console.log("Sample record structure:", filteredRecords[0]);
    }

    if (filteredRecords.length === 0) {
      toast({
        title: t("attendance.export.noRecordsFound"),
        description:
          selectedStatus === "all"
            ? t("attendance.export.noRecordsForDateRange")
            : t("attendance.export.noStatusRecordsForDateRange", {
                status: t(`attendance.status.${selectedStatus}`),
              }),
        variant: "destructive",
      });
      return;
    }

    const headers = [
      t("attendance.export.headers.dateTime"),
      t("attendance.export.headers.studentName"),
      t("attendance.export.headers.studentId"),
      t("attendance.export.headers.room"),
      t("attendance.export.headers.status"),
      t("attendance.export.headers.verification"),
    ];

    const rows = filteredRecords.map((record) => {
      // Safely handle date formatting with fallback
      let formattedDate = t("attendance.export.notAvailable");
      try {
        // Try timestamp first, then createdAt as fallback
        const dateValue = record.timestamp || record.createdAt;
        if (dateValue) {
          const date = new Date(dateValue);
          if (!isNaN(date.getTime())) {
            formattedDate = formatDate(date, "dd/MM/yyyy HH:mm", { locale: getDateLocale() });
          }
        }
      } catch (error) {
        console.warn("Invalid date in record:", record.timestamp || record.createdAt);
      }

      return [
        formattedDate,
        record.studentName || t("attendance.export.unknownStudent"),
        record.studentId || "N/A",
        record.roomName || t("attendance.export.unknownRoom"),
        t(`attendance.status.${record.status}`),
        record.verificationMethod === "manual"
          ? t("attendance.export.manualByTeacher")
          : record.verificationMethod
            ? record.verificationMethod.charAt(0).toUpperCase() + record.verificationMethod.slice(1)
            : t("attendance.export.notAvailable"),
      ];
    });

    const csvContent = [
      headers.join(","),
      ...rows.map((row) =>
        row.map((cell) => `"${cell.replace(/"/g, '""')}"`).join(",")
      ),
    ].join("\n");

    downloadFile(
      csvContent,
      `${t("attendance.export.attendanceReport")}_${formatDate(new Date(), "yyyy-MM-dd")}.csv`,
      "text/csv"
    );
    showExportSuccess("CSV");
  };

  // Export attendance to PDF - Mobile-friendly version
  const exportToPDF = async () => {
    const filteredRecords = getFilteredRecords();

    if (filteredRecords.length === 0) {
      toast({
        title: t("attendance.export.noRecordsFound"),
        description:
          selectedStatus === "all"
            ? t("attendance.export.noRecordsForDateRange")
            : t("attendance.export.noStatusRecordsForDateRange", {
                status: t(`attendance.status.${selectedStatus}`),
              }),
        variant: "destructive",
      });
      return;
    }

    try {
      // Show loading toast
      toast({
        title: t("attendance.export.generatingPDF"),
        description: t("attendance.export.pleaseWait"),
        duration: 5000, // Auto-dismiss after 5 seconds as fallback
      });

      // Create a temporary container for the PDF content
      const tempContainer = document.createElement('div');
      tempContainer.style.position = 'absolute';
      tempContainer.style.left = '-9999px';
      tempContainer.style.top = '0';
      tempContainer.style.width = '210mm'; // A4 width
      tempContainer.style.backgroundColor = 'white';
      tempContainer.style.padding = '20px';
      tempContainer.style.fontFamily = 'Arial, sans-serif';

      // Generate the PDF content HTML
      const pdfContent = generatePDFContent(filteredRecords);
      tempContainer.innerHTML = pdfContent;

      // Add to DOM temporarily
      document.body.appendChild(tempContainer);

      // Convert to canvas
      const canvas = await html2canvas(tempContainer, {
        scale: 2, // Higher quality
        useCORS: true,
        allowTaint: true,
        backgroundColor: '#ffffff',
        width: 794, // A4 width in pixels at 96 DPI
        height: Math.max(1123, tempContainer.scrollHeight * 2), // A4 height or content height
      });

      // Remove temporary container
      document.body.removeChild(tempContainer);

      // Create PDF
      const pdf = new jsPDF('p', 'mm', 'a4');
      const imgWidth = 210; // A4 width in mm
      const pageHeight = 297; // A4 height in mm
      const imgHeight = (canvas.height * imgWidth) / canvas.width;
      let heightLeft = imgHeight;
      let position = 0;

      // Add first page
      pdf.addImage(canvas.toDataURL('image/png'), 'PNG', 0, position, imgWidth, imgHeight);
      heightLeft -= pageHeight;

      // Add additional pages if needed
      while (heightLeft >= 0) {
        position = heightLeft - imgHeight;
        pdf.addPage();
        pdf.addImage(canvas.toDataURL('image/png'), 'PNG', 0, position, imgWidth, imgHeight);
        heightLeft -= pageHeight;
      }

      // Generate filename
      const periodText = selectedPeriod === "today" ? "Today" : "Yesterday";
      const selectedDate = selectedPeriod === "today" ? new Date() : subDays(new Date(), 1);
      const dateStr = formatDate(selectedDate, "yyyy-MM-dd");
      const filename = `${t("attendance.export.attendanceReport")}_${periodText}_${dateStr}.pdf`;

      // Save the PDF
      pdf.save(filename);

      // Show success
      showExportSuccess("PDF");

    } catch (error) {
      console.error("PDF generation error:", error);

      // Fallback to browser print for older devices
      toast({
        title: t("attendance.export.pdfGenerationError"),
        description: t("attendance.export.tryingBrowserPrint"),
        duration: 3000,
      });

      // Try browser print as fallback
      setTimeout(() => {
        const htmlContent = generateHTMLContent(true, filteredRecords);
        const printWindow = window.open("", "_blank");
        if (printWindow) {
          printWindow.document.write(htmlContent);
          printWindow.document.close();
          printWindow.onload = () => {
            setTimeout(() => {
              printWindow.print();
              printWindow.close();
            }, 500);
          };
        }
      }, 1000);
    }
  };

  // Export attendance to HTML
  const exportToHTML = () => {
    const filteredRecords = getFilteredRecords();

    if (filteredRecords.length === 0) {
      toast({
        title: t("attendance.export.noRecordsFound"),
        description:
          selectedStatus === "all"
            ? t("attendance.export.noRecordsForDateRange")
            : t("attendance.export.noStatusRecordsForDateRange", {
                status: t(`attendance.status.${selectedStatus}`),
              }),
        variant: "destructive",
      });
      return;
    }

    const htmlContent = generateHTMLContent(false, filteredRecords);
    downloadFile(
      htmlContent,
      `${t("attendance.export.attendanceReport")}_${formatDate(new Date(), "yyyy-MM-dd")}.html`,
      "text/html"
    );
    showExportSuccess("HTML");
  };

  return (
    <div className="flex flex-col gap-4 border rounded-lg p-4 shadow-sm">
      {/* Mobile View - Only visible on small screens */}
      <div className="md:hidden">
        {/* Header Section */}
        <div className="flex flex-col justify-between items-start mb-3">
          <div className="space-y-1 mb-2">
            <h3 className="font-medium text-base">
              {t("attendance.export.exportRecords")}
            </h3>
            <p className="text-xs text-muted-foreground">
              {t("attendance.export.selectDateRangeAndFormat")}
            </p>
          </div>
        </div>

        {/* Mobile Period and Filter Controls */}
        <div className="grid grid-cols-2 gap-2">
          {/* Period Selection - Mobile */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="outline"
                className="justify-start text-left font-normal text-xs h-9 px-2"
                size="sm"
              >
                <CalendarIcon className="mr-1 h-3 w-3 flex-shrink-0" />
                <span className="truncate">
                  {selectedPeriod === "today"
                    ? t("attendance.export.todaysRecords")
                    : t("attendance.export.yesterdaysRecords")
                  }
                </span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="w-44 p-1">
              <DropdownMenuLabel className="text-xs px-2 py-1">
                {t("attendance.export.selectPeriod")}
              </DropdownMenuLabel>
              <DropdownMenuSeparator className="my-1" />
              <DropdownMenuGroup>
                <DropdownMenuItem
                  onClick={() => setSelectedPeriod("today")}
                  className={cn(
                    "cursor-pointer text-xs py-1.5 px-2",
                    selectedPeriod === "today" && "bg-accent font-medium"
                  )}
                >
                  <span className="mr-1 flex-shrink-0">📅</span>
                  <span className="truncate">{t("attendance.export.todaysRecords")}</span>
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={() => setSelectedPeriod("yesterday")}
                  className={cn(
                    "cursor-pointer text-xs py-1.5 px-2",
                    selectedPeriod === "yesterday" && "bg-accent font-medium"
                  )}
                >
                  <span className="mr-1 flex-shrink-0">📆</span>
                  <span className="truncate">{t("attendance.export.yesterdaysRecords")}</span>
                </DropdownMenuItem>
              </DropdownMenuGroup>
            </DropdownMenuContent>
          </DropdownMenu>

          {/* Status Filter Dropdown - Mobile */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="outline"
                size="sm"
                className="col-span-2 h-9 px-2 text-xs"
              >
                <Filter className="mr-1 h-3 w-3 flex-shrink-0" />
                <span className="hidden xs:inline mr-1">
                  {t("attendance.export.filter")}:
                </span>
                <Badge
                  variant={
                    selectedStatus === "all"
                      ? "outline"
                      : (selectedStatus as any)
                  }
                  className="ml-0 text-[10px] px-1"
                >
                  {statusInfo[selectedStatus].icon}{" "}
                  <span className="truncate max-w-[80px]">
                    {statusInfo[selectedStatus].label}
                  </span>
                </Badge>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="w-44 p-1">
              <DropdownMenuLabel className="text-xs px-2 py-1">
                {t("attendance.export.filterByStatus")}
              </DropdownMenuLabel>
              <DropdownMenuSeparator className="my-1" />
              <DropdownMenuGroup>
                {(Object.keys(statusInfo) as StatusFilter[]).map((status) => (
                  <DropdownMenuItem
                    key={status}
                    onClick={() => setSelectedStatus(status)}
                    className={cn(
                      "cursor-pointer text-xs py-1.5 px-2",
                      selectedStatus === status && "bg-accent font-medium"
                    )}
                  >
                    <span className="mr-1 flex-shrink-0">
                      {statusInfo[status].icon}
                    </span>
                    <span className="truncate">{statusInfo[status].label}</span>
                  </DropdownMenuItem>
                ))}
              </DropdownMenuGroup>
            </DropdownMenuContent>
          </DropdownMenu>

          {/* Export Dropdown - Mobile */}
          <div className="col-span-2 mt-1">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="outline"
                  className="w-full h-9 text-xs bg-gradient-to-r from-blue-50 to-indigo-50 hover:from-blue-100 hover:to-indigo-100 border-blue-200 text-blue-700 hover:text-blue-800 transition-all duration-200"
                  disabled={recordsCount === 0}
                >
                  <Download className="mr-1 h-3 w-3" />
                  {t("attendance.export.export")}
                  <ChevronDown className="ml-1 h-3 w-3" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-48">
                <DropdownMenuItem
                  onClick={exportToHTML}
                  className="flex items-center gap-2 cursor-pointer hover:bg-blue-50 focus:bg-blue-50"
                >
                  <Globe className="h-3 w-3 text-blue-600" />
                  <span className="text-xs">{t("attendance.export.exportAsHTML")}</span>
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={exportToPDF}
                  className="flex items-center gap-2 cursor-pointer hover:bg-red-50 focus:bg-red-50"
                >
                  <FileImage className="h-3 w-3 text-red-600" />
                  <span className="text-xs">{t("attendance.export.exportAsPDF")}</span>
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={exportToCSV}
                  className="flex items-center gap-2 cursor-pointer hover:bg-green-50 focus:bg-green-50"
                >
                  <FileSpreadsheet className="h-3 w-3 text-green-600" />
                  <span className="text-xs">{t("attendance.export.exportAsCSV")}</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>

        {/* Mobile Results */}
        {(
          <div className="text-xs mt-3">
            {recordsCount > 0 ? (
              <div className="text-green-600 flex items-center gap-1 flex-wrap">
                <Check className="h-3 w-3 flex-shrink-0" />
                <span>
                  {t("attendance.export.recordsFound", { count: recordsCount })}
                </span>
                {selectedStatus !== "all" && (
                  <span className="ml-1 flex items-center flex-wrap">
                    <span>{t("attendance.export.withStatus")}</span>{" "}
                    <Badge
                      variant={selectedStatus as any}
                      className="ml-1 text-[10px]"
                    >
                      {statusInfo[selectedStatus].icon}{" "}
                      {t(`attendance.status.${selectedStatus}`)}
                    </Badge>
                  </span>
                )}
              </div>
            ) : (
              <Alert variant="warning" className="py-1 text-xs">
                <AlertCircle className="h-3 w-3 flex-shrink-0" />
                <AlertDescription>
                  {t("attendance.export.noRecordsAvailable")}
                </AlertDescription>
              </Alert>
            )}
          </div>
        )}
      </div>

      {/* Desktop View - Original Layout (Preserved Exactly) */}
      <div className="hidden md:block">
        <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center">
          <div className="flex-1 space-y-1">
            <h3 className="font-medium">
              {t("attendance.export.exportRecords")}
            </h3>
            <p className="text-sm text-muted-foreground">
              {t("attendance.export.selectDateRangeAndFormat")}
            </p>
          </div>
          <div className="flex flex-wrap gap-2">
            {/* Period Selection - Desktop */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="outline"
                  className="justify-start text-left font-normal"
                  size="sm"
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {selectedPeriod === "today"
                    ? t("attendance.export.todaysRecords")
                    : t("attendance.export.yesterdaysRecords")
                  }
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-48 p-1">
                <DropdownMenuLabel className="text-sm px-2 py-1">
                  {t("attendance.export.selectPeriod")}
                </DropdownMenuLabel>
                <DropdownMenuSeparator className="my-1" />
                <DropdownMenuGroup>
                  <DropdownMenuItem
                    onClick={() => setSelectedPeriod("today")}
                    className={cn(
                      "cursor-pointer text-sm py-2 px-2",
                      selectedPeriod === "today" && "bg-accent font-medium"
                    )}
                  >
                    <span className="mr-2 flex-shrink-0">📅</span>
                    <span className="truncate">{t("attendance.export.todaysRecords")}</span>
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    onClick={() => setSelectedPeriod("yesterday")}
                    className={cn(
                      "cursor-pointer text-sm py-2 px-2",
                      selectedPeriod === "yesterday" && "bg-accent font-medium"
                    )}
                  >
                    <span className="mr-2 flex-shrink-0">📆</span>
                    <span className="truncate">{t("attendance.export.yesterdaysRecords")}</span>
                  </DropdownMenuItem>
                </DropdownMenuGroup>
              </DropdownMenuContent>
            </DropdownMenu>

            {/* Status Filter Dropdown */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm" className="gap-2">
                  <Filter className="h-4 w-4" />
                  <span className="hidden sm:inline">
                    {t("attendance.export.filter")}:
                  </span>
                  <Badge
                    variant={
                      selectedStatus === "all"
                        ? "outline"
                        : (selectedStatus as any)
                    }
                    className="ml-1"
                  >
                    {statusInfo[selectedStatus].icon}{" "}
                    {statusInfo[selectedStatus].label}
                  </Badge>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-56">
                <DropdownMenuLabel>
                  {t("attendance.export.filterByStatus")}
                </DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuGroup>
                  {(Object.keys(statusInfo) as StatusFilter[]).map((status) => (
                    <DropdownMenuItem
                      key={status}
                      onClick={() => setSelectedStatus(status)}
                      className={cn(
                        "cursor-pointer",
                        selectedStatus === status && "bg-accent font-medium"
                      )}
                    >
                      <span className="mr-2">{statusInfo[status].icon}</span>
                      {statusInfo[status].label}
                    </DropdownMenuItem>
                  ))}
                </DropdownMenuGroup>
              </DropdownMenuContent>
            </DropdownMenu>

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="outline"
                  className="flex items-center gap-2 bg-gradient-to-r from-blue-50 to-indigo-50 hover:from-blue-100 hover:to-indigo-100 border-blue-200 text-blue-700 hover:text-blue-800 transition-all duration-200"
                  disabled={recordsCount === 0}
                >
                  <Download className="h-4 w-4" />
                  {t("attendance.export.export")}
                  <ChevronDown className="h-4 w-4 ml-1" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-48">
                <DropdownMenuItem
                  onClick={exportToHTML}
                  className="flex items-center gap-2 cursor-pointer hover:bg-blue-50 focus:bg-blue-50"
                >
                  <Globe className="h-4 w-4 text-blue-600" />
                  <span>{t("attendance.export.exportAsHTML")}</span>
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={exportToPDF}
                  className="flex items-center gap-2 cursor-pointer hover:bg-red-50 focus:bg-red-50"
                >
                  <FileImage className="h-4 w-4 text-red-600" />
                  <span>{t("attendance.export.exportAsPDF")}</span>
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={exportToCSV}
                  className="flex items-center gap-2 cursor-pointer hover:bg-green-50 focus:bg-green-50"
                >
                  <FileSpreadsheet className="h-4 w-4 text-green-600" />
                  <span>{t("attendance.export.exportAsCSV")}</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>

        {(
          <div className="text-sm mt-4">
            {recordsCount > 0 ? (
              <div className="text-green-600 flex items-center gap-1">
                <Check className="h-4 w-4" />
                {t("attendance.export.recordsFound", { count: recordsCount })}
                {selectedStatus !== "all" && (
                  <span className="ml-1">
                    {t("attendance.export.withStatus")}{" "}
                    <Badge variant={selectedStatus as any}>
                      {statusInfo[selectedStatus].icon}{" "}
                      {t(`attendance.status.${selectedStatus}`)}
                    </Badge>
                  </span>
                )}
              </div>
            ) : records.length > 0 ? (
              <Alert variant="warning" className="py-2">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  {selectedStatus === "all"
                    ? t("attendance.export.noRecordsForPeriod")
                    : t("attendance.export.noStatusRecordsForPeriod", {
                        status: t(`attendance.status.${selectedStatus}`),
                      })}
                </AlertDescription>
              </Alert>
            ) : (
              <Alert variant="warning" className="py-2">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  {t("attendance.export.noRecordsAvailable")}
                </AlertDescription>
              </Alert>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
