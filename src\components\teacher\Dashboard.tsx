import { useState, useEffect, ReactNode, useCallback } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { AttendanceR<PERSON>ord, Student, Course } from "@/lib/types";
import { format } from "date-fns";
import {
  AlertTriangle,
  Bell,
  Users,
  RefreshCw,
  Check,
  AlertCircle,
  Grid,
  LayoutGrid,
  LayoutList,
  RefreshCcw,
  UserCheck,
  UserX,
  Clock,
  FileCheck,
  Loader2,
  ChevronDown,
  Plus,
  Search,
  X,
} from "lucide-react";
import { toast } from "@/lib/utils/toast";
import { createBulkLocalizedNotifications, createLocalizedNotification } from "@/lib/utils/notification-localization";
import { StatisticsCards } from "./dashboard/StatisticsCards";
import { StudentListView } from "./dashboard/StudentListView";
import { StudentGridView } from "./dashboard/StudentGridView";
import { getStatusIcon } from "./dashboard/utils/statusIcons";
import { supabase, safeRemoveChannels } from "@/lib/supabase";
import { useAuth } from "@/context/AuthContext";
import { Button } from "@/components/ui/button";
import { Database } from "@/lib/database.types";
import { AttendanceExport } from "./AttendanceExport";
import { cn } from "@/lib/utils";
import { Input } from "@/components/ui/input";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import { RoomLocationSettings } from "./RoomLocationSettings";
import { StatsCard } from "./dashboard/StatsCard";
import { EnhancedStatsCard } from "./dashboard/EnhancedStatsCard";
import { BlockSelector } from "./BlockSelector";

import { useTranslation } from "react-i18next";
import { showAttendanceStatusNotification } from "@/components/notifications/AttendanceStatusNotification";

interface Room {
  id: string;
  name: string;
  building: string | null;
  floor: number;
  capacity: number;
  teacher_id: string;
  current_qr_code: string | null;
  qr_expiry: string | null;
  created_at?: string;
  updated_at?: string;
}

export default function TeacherDashboard() {
  const { t } = useTranslation();
  const [selectedRoom, setSelectedRoom] = useState<string>("");
  const [students, setStudents] = useState<Student[]>([]);
  const [rooms, setRooms] = useState<Room[]>([]);
  const [attendanceRecords, setAttendanceRecords] = useState<{
    [key: string]: AttendanceRecord;
  }>({});
  const [viewMode, setViewMode] = useState<"list" | "grid">(() => {
    // Load view mode preference from localStorage
    const savedViewMode = localStorage.getItem("teacherDashboardViewMode");
    return (savedViewMode as "list" | "grid") || "list";
  });
  const [todayStats, setTodayStats] = useState({
    present: 0,
    absent: 0,
    late: 0,
    excused: 0,
    total: 0,
  });
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const { profile } = useAuth();
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedBlock, setSelectedBlock] = useState<string | null>(null);
  const [showOnlyAbsent, setShowOnlyAbsent] = useState(false);
  const [blocks, setBlocks] = useState<any[]>([]);
  const [lastNotificationSent, setLastNotificationSent] = useState<Date | null>(
    null
  );

  // Fetch blocks and rooms
  useEffect(() => {
    const fetchBlocksAndRooms = async () => {
      if (!profile?.id) return;

      try {
        // Fetch blocks for the current school
        let blocksQuery = supabase.from("blocks").select("*").order("name");

        // Filter by school_id if available
        if (profile?.school_id) {
          blocksQuery = blocksQuery.eq("school_id", profile.school_id);
        }

        const { data: blocksData, error: blocksError } = await blocksQuery;

        if (blocksError) throw blocksError;
        setBlocks(blocksData || []);

        // Continue with fetching rooms
      } catch (error) {
        console.error("Error fetching blocks:", error);
        toast.error(
          t("common.error"),
          {
            description: t("teacher.dashboard.failedToFetchBlocks"),
          }
        );
      }
    };

    fetchBlocksAndRooms();
  }, [profile?.id]);

  // Fetch rooms associated with the teacher
  useEffect(() => {
    const fetchRooms = async () => {
      if (!profile?.id) return;

      try {
        // Fetch rooms for the current school
        let roomsQuery = supabase
          .from("rooms")
          .select("*")
          .eq("teacher_id", profile.id);

        // Filter by school_id if available
        if (profile?.school_id) {
          roomsQuery = roomsQuery.eq("school_id", profile.school_id);
        }

        const { data: teacherRooms, error } = await roomsQuery;

        if (error) throw error;

        if (teacherRooms && teacherRooms.length > 0) {
          setRooms(teacherRooms as Room[]);
          // Don't set a default room on initial load
          // This will show all students by default
        } else {
          // If no rooms found, create a default room for the teacher
          // Create room data with school_id
          const roomData: any = {
            name: "Default Room",
            building: "Main Building",
            floor: 1,
            capacity: 30,
            teacher_id: profile.id,
          };

          // Add school_id if available
          if (profile?.school_id) {
            roomData.school_id = profile.school_id;
          }

          // First, get or create a default block
          let blockId = null;

          // Try to find an existing block
          let blocksQuery = supabase
            .from("blocks")
            .select("id")
            .order("name")
            .limit(1);

          // Filter by school_id if available
          if (profile?.school_id) {
            blocksQuery = blocksQuery.eq("school_id", profile.school_id);
          }

          const { data: existingBlocks, error: blockError } = await blocksQuery;

          if (blockError) {
            console.error("Error fetching blocks:", blockError);
            throw blockError;
          }

          if (existingBlocks && existingBlocks.length > 0) {
            // Use the first available block
            blockId = existingBlocks[0].id;
          } else {
            // Create a new default block
            const blockData: any = {
              name: "Default Block",
            };

            // Add school_id if available
            if (profile?.school_id) {
              blockData.school_id = profile.school_id;
            }

            const { data: newBlock, error: createBlockError } = await supabase
              .from("blocks")
              .insert([blockData])
              .select()
              .single();

            if (createBlockError) {
              console.error("Error creating default block:", createBlockError);
              throw createBlockError;
            }

            blockId = newBlock.id;
          }

          // Add block_id to room data
          roomData.block_id = blockId;

          const { data: newRoom, error: createError } = await supabase
            .from("rooms")
            .insert([roomData])
            .select()
            .single();

          if (createError) throw createError;

          if (newRoom) {
            setRooms([newRoom as Room]);
            // Don't set a default room on initial load
          }
        }
      } catch (error: any) {
        console.error("Error fetching rooms:", error);

        // If the rooms table doesn't exist yet, show a more specific error
        if (error.message?.includes("does not exist")) {
          toast.error(
            t("teacher.dashboard.setupRequired"),
            {
              description: t("teacher.dashboard.setupRequiredDescription"),
            }
          );
        } else {
          toast.error(
            t("common.error"),
            {
              description: t("teacher.dashboard.failedToFetchRooms"),
            }
          );
        }
      }
    };

    fetchRooms();
  }, [profile?.id]);

  const calculateStats = (
    records: Record<string, AttendanceRecord>,
    studentList: Student[]
  ) => {
    if (!studentList || studentList.length === 0) return todayStats;

    const totalStudents = studentList.length;

    // Count students by status
    const studentStatuses = studentList.reduce((acc, student) => {
      const status = records[student.id]?.status || "absent";
      acc[status] = (acc[status] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    // Ensure all status counts are initialized
    const stats = {
      present: studentStatuses.present || 0,
      absent: studentStatuses.absent || 0,
      late: studentStatuses.late || 0,
      excused: studentStatuses.excused || 0,
      total: totalStudents,
    };

    // If we don't have records for all students, make sure absent count is correct
    if (
      stats.present + stats.absent + stats.late + stats.excused <
      totalStudents
    ) {
      stats.absent =
        totalStudents - (stats.present + stats.late + stats.excused);
    }

    // Stats calculated

    return stats;
  };

  const handleStatusUpdate = async (
    studentId: string,
    newStatus: "present" | "absent" | "late" | "excused"
  ) => {
    const student = students.find((s) => s.id === studentId);
    if (!student) return;

    // Get the current status before updating
    const currentStatus = getAttendanceStatus(studentId);

    try {
      // Update attendance records with the new status in local state first
      setAttendanceRecords((prev) => {
        const newRecords = {
          ...prev,
          [studentId]: {
            ...(prev[studentId] || {
              id: "", // Will be updated when the database responds
              studentId: studentId,
              roomId: student.roomId || student.room_id || selectedRoom || "",
              timestamp: new Date().toISOString(),
              deviceInfo: "Manual update by teacher",
              location: null,
              verificationMethod: "manual",
            }),
            status: newStatus,
          },
        };

        // Update statistics immediately
        setTodayStats(calculateStats(newRecords, students));

        // Track this student as recently updated to prevent polling from overriding it
        // This is using the recentlyUpdatedStudents Map from the polling interval
        if (typeof recentlyUpdatedStudents !== "undefined") {
          recentlyUpdatedStudents.set(studentId, Date.now());

          // Set a timeout to remove this student from the protection after 2 minutes
          setTimeout(() => {
            recentlyUpdatedStudents.delete(studentId);
          }, 120000); // 2 minutes
        }

        return newRecords;
      });

      // Get today's date at midnight for comparison
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      const todayStr = today.toISOString();

      // Determine which room ID to use - we need a valid room ID
      let roomId = student.roomId || student.room_id || selectedRoom || "";

      // If we still don't have a valid room ID, try to find one from the rooms array
      if (!roomId) {
        // No direct room ID available for student

        // Try to find a room for this student
        if (rooms.length > 0) {
          // Use the first available room as a fallback
          roomId = rooms[0].id;
          // Using fallback room ID for student
        } else {
          // If we still don't have a room, we can't proceed with the database update
          throw new Error("No valid room ID available for attendance update");
        }
      }

      // Using room ID for student

      // Validate that we have a non-empty room ID
      if (!roomId || roomId === "") {
        throw new Error("Empty room ID would cause a 400 error");
      }

      // Check for existing record today - don't filter by room_id to avoid 400 errors
      const { data: existingRecords, error: fetchError } = await supabase
        .from("attendance_records")
        .select("id, status, room_id")
        .eq("student_id", studentId)
        .gte("timestamp", todayStr);

      // Found existing records

      if (fetchError) {
        console.error("Error fetching attendance records:", fetchError);
        throw fetchError;
      }

      const now = new Date().toISOString();
      let recordId: string;
      const previousStatus =
        existingRecords && existingRecords.length > 0
          ? existingRecords[0].status
          : "absent";

      // Let the database trigger handle notifications
      try {
        if (existingRecords && existingRecords.length > 0) {
          // Update existing record
          recordId = existingRecords[0].id;
          const { error } = await supabase
            .from("attendance_records")
            .update({
              status: newStatus,
              verification_method: "manual",
              device_info: `Manual update by teacher, teacher_id:${
                profile?.id || ""
              }`,
              timestamp: now,
              room_id: roomId, // Ensure room_id is included
            })
            .eq("id", recordId);

          if (error) {
            console.error("Error updating attendance record:", error);
            throw error;
          }
        } else {
          // Create new record
          try {
            const recordData: any = {
              student_id: studentId,
              room_id: roomId,
              timestamp: now,
              device_info: `Manual update by teacher, teacher_id:${
                profile?.id || ""
              }`,
              verification_method: "manual",
              status: newStatus,
              location: null,
            };

            // Add school_id if available (required for RLS policies)
            if (profile?.school_id) {
              recordData.school_id = profile.school_id;
            }

            const { data, error } = await supabase
              .from("attendance_records")
              .insert(recordData)
              .select("id")
              .single();

            if (error) {
              console.error("Error inserting attendance record:", error);
              throw error;
            }
            recordId = data.id;
          } catch (insertError) {
            console.error("Exception during insert:", insertError);
            throw insertError;
          }
        }

        // Create a localized notification for the student
        try {
          const teacherName = profile?.name || "Teacher";

          // Get room name
          let roomName = "Unknown";
          if (roomId) {
            const { data: roomData } = await supabase
              .from("rooms")
              .select("name")
              .eq("id", roomId)
              .single();

            if (roomData) {
              roomName = roomData.name;
            }
          }

          // Use the localized notification utility
          let templateKey: "markedPresent" | "markedAbsent" | "markedLate" | "markedExcused";
          let notificationType: "attendance" | "absence" | "late" | "excused";

          switch (newStatus) {
            case "present":
              templateKey = "markedPresent";
              notificationType = "attendance";
              break;
            case "absent":
              templateKey = "markedAbsent";
              notificationType = "absence";
              break;
            case "late":
              templateKey = "markedLate";
              notificationType = "late";
              break;
            case "excused":
              templateKey = "markedExcused";
              notificationType = "excused";
              break;
            default:
              templateKey = "markedAbsent";
              notificationType = "absence";
              break;
          }

          const notificationResult = await createLocalizedNotification({
            studentId: student.id,
            teacherId: profile?.id,
            type: notificationType,
            templateKey,
            templateParams: [roomName, teacherName],
            metadata: {
              status: newStatus,
              previous_status: currentStatus,
              updated_by: "teacher",
              room_id: roomId,
              room_name: roomName,
            },
            roomNumber: roomName,
          });

        } catch (notificationError) {
          console.error("Error creating localized notification:", notificationError);
          // Continue execution even if notification creation fails
        }
      } catch (error) {
        console.error("Error updating attendance record:", error);
        throw error;
      }

      // Show a beautiful toast notification based on the status
      const getStatusEmoji = (status: string) => {
        switch (status) {
          case "present":
            return "✅";
          case "absent":
            return "❌";
          case "late":
            return "⏰";
          case "excused":
            return "📝";
          default:
            return "📋";
        }
      };

      const getStatusColor = (status: string) => {
        switch (status) {
          case "present":
            return "text-green-500";
          case "absent":
            return "text-red-500";
          case "late":
            return "text-amber-500";
          case "excused":
            return "text-blue-500";
          default:
            return "text-gray-500";
        }
      };

      // Show attendance status notification
      showAttendanceStatusNotification(t, {
        studentName: student.name,
        status: newStatus
      });

      // Save updated data to localStorage and dispatch sync event
      try {
        const dashboardData = {
          students: students,
          attendanceRecords: attendanceRecords,
          timestamp: Date.now(),
          source: "TeacherDashboard-ManualUpdate"
        };
        localStorage.setItem("teacherDashboardData", JSON.stringify(dashboardData));

        // Dispatch sync event to notify StudentDirectory
        const syncEvent = new CustomEvent("dashboard-data-updated", {
          detail: {
            ...dashboardData,
            updatedStudentId: studentId,
            newStatus: newStatus
          }
        });
        window.dispatchEvent(syncEvent);
        // Saved to localStorage and dispatched sync event

        // Also dispatch the legacy attendance-updated event for compatibility
        const event = new CustomEvent("attendance-updated", {
          detail: {
            studentId: studentId,
            status: newStatus,
            source: "TeacherDashboard",
          },
        });
        window.dispatchEvent(event);
        // Dispatched attendance-updated event
      } catch (eventError) {
        console.error("Error dispatching events:", eventError);
      }
    } catch (error: any) {
      console.error("Error updating attendance status:", error);

      // Show error toast with more specific message
      let errorMessage = t("teacher.attendance.failedToUpdate", {
        name: student.name,
        status: newStatus,
      });

      // Add more specific error details if available
      if (error.message) {
        if (error.message.includes("room ID")) {
          errorMessage = t("teacher.attendance.roomAssignmentIssue", {
            message: error.message,
            name: student.name,
          });
        } else {
          errorMessage += ` Error: ${error.message}`;
        }
      }

      // If it's a Supabase error with details
      if (error.details) {
        errorMessage += ` Details: ${error.details}`;
      }

      toast.error(
        t("teacher.attendance.errorUpdatingStatus"),
        {
          description: errorMessage,
          duration: 5000,
        }
      );

      // Revert the local state change
      setAttendanceRecords((prev) => {
        // If we had a previous record, restore it
        if (prev[studentId] && currentStatus) {
          return {
            ...prev,
            [studentId]: {
              ...prev[studentId],
              status: currentStatus,
            },
          };
        }

        // Otherwise remove the record
        const newRecords = { ...prev };
        delete newRecords[studentId];
        return newRecords;
      });
    }
  };

  // Fetch data when room changes
  useEffect(() => {
    // Always fetch data, even if no room is selected
    fetchAttendanceData();
  }, [selectedRoom]);

  // Handler for attendance record changes from Supabase
  const handleAttendanceRecordChange = useCallback(
    async (payload: any) => {
      // Attendance record changed

      if (payload.eventType === "UPDATE" || payload.eventType === "INSERT") {
        // Update the specific student's attendance status
        const newRecord = payload.new;

        // Only process records from today
        const recordDate = new Date(newRecord.timestamp);
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        const tomorrow = new Date(today.getTime() + 24 * 60 * 60 * 1000);

        // Check if the record is from today
        if (recordDate >= today && recordDate < tomorrow) {
          // Update the attendance records state
          setAttendanceRecords((prev) => {
            const updatedRecords = {
              ...prev,
              [newRecord.student_id]: {
                id: newRecord.id,
                studentId: newRecord.student_id,
                roomId: newRecord.room_id,
                timestamp: newRecord.timestamp,
                deviceInfo: newRecord.device_info || "",
                location: newRecord.location || null,
                verificationMethod: newRecord.verification_method as
                  | "biometric"
                  | "pin"
                  | "manual",
                status: newRecord.status as
                  | "present"
                  | "absent"
                  | "late"
                  | "excused",
              },
            };

            // Recalculate statistics with the updated records
            setTodayStats(calculateStats(updatedRecords, students));

            // Save updated data to localStorage
            const dashboardData = {
              students: students,
              attendanceRecords: updatedRecords,
              timestamp: Date.now(),
              source: "TeacherDashboard-RealTime"
            };
            localStorage.setItem("teacherDashboardData", JSON.stringify(dashboardData));

            // Dispatch event to notify StudentDirectory of real-time update
            const event = new CustomEvent("dashboard-data-updated", {
              detail: {
                ...dashboardData,
                updatedStudentId: newRecord.student_id,
                newStatus: newRecord.status
              }
            });
            window.dispatchEvent(event);

            return updatedRecords;
          });

          // Show a notification for the teacher if it's not a manual update
          const affectedStudent = students.find(
            (s) => s.id === newRecord.student_id
          );
          if (affectedStudent && newRecord.verification_method !== "manual") {
            toast.success(
              t("teacher.attendance.attendanceUpdated"),
              {
                description: t("teacher.attendance.studentVerifiedPresence", {
                  name: affectedStudent.name,
                }),
                duration: 4000,
              }
            );
          }
        } else {
          // Record is not from today, ignore it
          console.log("Ignoring attendance record from", recordDate.toDateString(), "- not from today");
          return;
        }
      }
    },
    [students]
  );

  // Set up real-time subscription for attendance updates
  useEffect(() => {
    // Always set up the subscription, even if no room is selected

    // Variable to store the timeout ID for debouncing
    let refreshTimeout: NodeJS.Timeout | null = null;

    // Listen for custom attendance-updated events from the StudentDirectory component
    const handleAttendanceUpdated = async (event: Event) => {
      const customEvent = event as CustomEvent<{
        studentId: string;
        status: "present" | "absent" | "late" | "excused";
        source?: string;
        records?: Record<string, any>;
      }>;

      // Dashboard: Received attendance-updated event

      // If the event is from StudentDirectory, handle the database update
      if (customEvent.detail.source === "StudentDirectory") {
        // Handling database update for StudentDirectory event

        // Use the existing handleStatusUpdate function to handle the database update
        try {
          await handleStatusUpdate(customEvent.detail.studentId, customEvent.detail.status);
          // Successfully updated attendance status in database
        } catch (error) {
          console.error("Dashboard: Error updating attendance status:", error);
        }
      } else {
        // For other sources, just refresh the data
        // Debounce the refresh to avoid multiple rapid refreshes
        if (refreshTimeout) {
          clearTimeout(refreshTimeout);
        }

        refreshTimeout = setTimeout(() => {
          fetchAttendanceData();
        }, 300);
      }
    };

    // Add event listener for custom events
    window.addEventListener("attendance-updated", handleAttendanceUpdated);

    // Listen for data requests from StudentDirectory
    const handleDataRequest = (event: Event) => {
      const customEvent = event as CustomEvent<{ source: string }>;

      if (customEvent.detail.source === "StudentDirectory") {
        // Save current data to localStorage
        const dashboardData = {
          students: students,
          attendanceRecords: attendanceRecords,
          timestamp: Date.now(),
          source: "TeacherDashboard-DataRequest"
        };
        localStorage.setItem("teacherDashboardData", JSON.stringify(dashboardData));

        // Send current data to StudentDirectory
        const dataEvent = new CustomEvent("dashboard-data-updated", {
          detail: dashboardData
        });
        window.dispatchEvent(dataEvent);
      }
    };

    window.addEventListener("request-dashboard-data", handleDataRequest);

    // Create a map to track recently updated student IDs and timestamps
    const recentlyUpdatedStudents = new Map<string, number>();

    // Set up a polling interval to refresh data periodically (every 30 seconds)
    const pollingInterval = setInterval(() => {
      // Polling for attendance updates

      // Use a modified version of fetchAttendanceData that respects recent manual changes
      const fetchWithProtection = async () => {
        try {
          // Get today's date at midnight
          const today = new Date();
          today.setHours(0, 0, 0, 0);
          const todayStr = today.toISOString();
          const tomorrowStr = new Date(
            today.getTime() + 24 * 60 * 60 * 1000
          ).toISOString();

          // Make sure we have rooms data first
          if (rooms.length === 0) {
            // No rooms data yet, skipping polling update
            return;
          }

          // Get all student IDs for the query
          const studentIds = students.map((student) => student.id);

          if (studentIds.length === 0) {
            // No students to fetch attendance records for
            return;
          }

          // Fetch TODAY'S attendance records for polling update
          let recordsQuery = supabase
            .from("attendance_records")
            .select("*")
            .in("student_id", studentIds)
            .gte("timestamp", todayStr)
            .lt("timestamp", tomorrowStr)
            .order("timestamp", { ascending: false });

          // Filter by school_id if available
          if (profile?.school_id) {
            recordsQuery = recordsQuery.eq("school_id", profile.school_id);
          }

          const { data: records, error: recordsError } = await recordsQuery;

          if (recordsError) {
            console.error("Error fetching attendance records:", recordsError);
            return;
          }

          // No need to check for recordsError here, we already handled it above

          // Fetched attendance records

          // Process attendance records - get the most recent record for each student
          const formattedRecords: Record<string, AttendanceRecord> = {};

          // Group records by student_id
          const recordsByStudent: Record<string, any[]> = {};
          (records || []).forEach((record) => {
            if (!recordsByStudent[record.student_id]) {
              recordsByStudent[record.student_id] = [];
            }
            recordsByStudent[record.student_id].push(record);
          });

          // For each student, get the most recent record
          Object.entries(recordsByStudent).forEach(
            ([studentId, studentRecords]) => {
              // Sort records by timestamp in descending order (newest first)
              const sortedRecords = studentRecords.sort(
                (a, b) =>
                  new Date(b.timestamp).getTime() -
                  new Date(a.timestamp).getTime()
              );

              // Use the most recent record
              const mostRecent = sortedRecords[0];

              // Check if this student was recently updated manually
              const lastManualUpdate = recentlyUpdatedStudents.get(studentId);
              const recordTimestamp = new Date(mostRecent.timestamp).getTime();

              // If this student was manually updated recently and the database record is older,
              // don't override the local state
              if (lastManualUpdate && recordTimestamp < lastManualUpdate) {
                // Skipping update for recently modified student
                return;
              }

              formattedRecords[studentId] = {
                id: mostRecent.id,
                studentId: mostRecent.student_id,
                roomId: mostRecent.room_id,
                timestamp: mostRecent.timestamp,
                deviceInfo: mostRecent.device_info || "",
                location: mostRecent.location || null,
                verificationMethod: mostRecent.verification_method as
                  | "biometric"
                  | "pin"
                  | "manual",
                status: mostRecent.status as
                  | "present"
                  | "absent"
                  | "late"
                  | "excused",
              };
            }
          );

          // Update attendance records, but only for students that weren't recently updated manually
          setAttendanceRecords((prev) => {
            const newRecords = { ...prev };

            // Only update records for students that weren't recently updated manually
            Object.entries(formattedRecords).forEach(([studentId, record]) => {
              const lastManualUpdate = recentlyUpdatedStudents.get(studentId);
              const recordTimestamp = new Date(record.timestamp).getTime();

              if (!lastManualUpdate || recordTimestamp > lastManualUpdate) {
                newRecords[studentId] = record;
              }
            });

            return newRecords;
          });

          // Update statistics
          setAttendanceRecords((records) => {
            setTodayStats(calculateStats(records, students));
            return records;
          });
        } catch (error) {
          console.error("Error in polling update:", error);
        }
      };

      fetchWithProtection();
    }, 30000); // Changed from 5 seconds to 30 seconds

    // Set up channel for attendance updates
    let channel;

    if (selectedRoom) {
      // If a room is selected, only listen for changes in that room
      channel = supabase
        .channel("teacher-dashboard-updates")
        .on(
          "postgres_changes",
          {
            event: "*", // Listen for all events (INSERT, UPDATE, DELETE)
            schema: "public",
            table: "attendance_records",
            filter: `room_id=eq.${selectedRoom}`,
          },
          async (payload) => handleAttendanceRecordChange(payload)
        )
        .subscribe();
    } else {
      // If no room is selected, listen for all attendance changes
      channel = supabase
        .channel("teacher-dashboard-updates")
        .on(
          "postgres_changes",
          {
            event: "*", // Listen for all events (INSERT, UPDATE, DELETE)
            schema: "public",
            table: "attendance_records",
          },
          async (payload) => handleAttendanceRecordChange(payload)
        )
        .subscribe();
    }

    // Also subscribe to notifications table for student alerts
    const notificationsChannel = supabase
      .channel("teacher-notifications-updates")
      .on(
        "postgres_changes",
        {
          event: "INSERT",
          schema: "public",
          table: "notifications",
        },
        async (payload) => {
          // New notification created
        }
      )
      .subscribe();

    // Clean up subscriptions
    return () => {
      // Remove event listeners
      window.removeEventListener("attendance-updated", handleAttendanceUpdated);
      window.removeEventListener("request-dashboard-data", handleDataRequest);

      // Clear the polling interval
      clearInterval(pollingInterval);

      // Remove Supabase channels
      safeRemoveChannels(channel, notificationsChannel);
    };
  }, [selectedRoom, students, handleAttendanceRecordChange]);

  const getAttendanceStatus = (
    studentId: string
  ): "present" | "absent" | "late" | "excused" => {
    const record = attendanceRecords[studentId] as AttendanceRecord | undefined;
    return record?.status || "absent";
  };

  const handleNotifyAbsent = async () => {
    try {
      // Make sure we have students data
      if (students.length === 0) {
        toast.error(
          t("teacher.attendance.noStudentsAvailable"),
          {
            description: t("teacher.attendance.pleaseWaitForData"),
          }
        );
        return;
      }

      // Filter students based on the current selection in the block selector
      let targetStudents: Student[] = [];
      let targetName = t("teacher.dashboard.allStudents");
      let targetType = "all";

      if (selectedRoom) {
        // If a specific room is selected, only notify students in that room
        targetStudents = students.filter((student) => {
          // Get the selected room object
          const selectedRoomObj = rooms.find((r) => r.id === selectedRoom);
          const selectedRoomName = selectedRoomObj?.name || "";

          // Check if student is in the selected room using multiple methods
          let isInSelectedRoom = false;

          // Method 1: Direct room_id match
          if (
            student.roomId === selectedRoom ||
            student.room_id === selectedRoom
          ) {
            isInSelectedRoom = true;
          }
          // Method 2: Room number match (string comparison)
          else if (
            student.roomNumber &&
            selectedRoomName &&
            (student.roomNumber === selectedRoomName ||
              student.roomNumber === `Room ${selectedRoomName}` ||
              student.roomNumber.includes(selectedRoomName) ||
              // Handle case where room number is stored without "Room " prefix
              selectedRoomName === student.roomNumber ||
              `Room ${selectedRoomName}` === student.roomNumber ||
              // Handle numeric comparison
              student.roomNumber.toString() === selectedRoomName.toString())
          ) {
            isInSelectedRoom = true;
          }
          // Method 3: Check if the room name in the student profile matches the selected room name
          else if (selectedRoomObj && student.room_number) {
            // Try different formats of room number comparison
            if (
              student.room_number === selectedRoomObj.name ||
              student.room_number === `Room ${selectedRoomObj.name}` ||
              student.room_number.includes(selectedRoomObj.name) ||
              selectedRoomObj.name.includes(student.room_number)
            ) {
              isInSelectedRoom = true;
            }
          }

          const isAbsent = getAttendanceStatus(student.id) === "absent";

          // Student room assignment checked

          return isInSelectedRoom && isAbsent;
        });
        const room = rooms.find((r) => r.id === selectedRoom);
        targetName = room ? `Room ${room.name}` : "Selected Room";
        targetType = "room";
      } else if (selectedBlock) {
        // If only a block is selected (no specific room), notify all absent students in that block
        targetStudents = students.filter((student) => {
          // Check multiple ways a student could be in the selected block
          let isInSelectedBlock = false;

          // Direct block_id match
          if (student.block_id === selectedBlock) {
            isInSelectedBlock = true;
          }
          // Check if student's room belongs to the selected block
          else if (student.room_id) {
            const studentRoom = rooms.find((r) => r.id === student.room_id);
            if (studentRoom && studentRoom.block_id === selectedBlock) {
              isInSelectedBlock = true;
            }
          }
          // Check if student's blockName contains the selected block name
          else if (student.blockName) {
            const selectedBlockObj = blocks.find((b) => b.id === selectedBlock);
            if (
              selectedBlockObj &&
              student.blockName.includes(selectedBlockObj.name)
            ) {
              isInSelectedBlock = true;
            }
          }

          const isAbsent = getAttendanceStatus(student.id) === "absent";

          // Student block assignment checked

          return isInSelectedBlock && isAbsent;
        });

        // Get the block name for display
        const { data: blockData } = await supabase
          .from("blocks")
          .select("name")
          .eq("id", selectedBlock)
          .single();

        targetName = blockData ? `Block ${blockData.name}` : "Selected Block";
        targetType = "block";
      } else {
        // If no block or room is selected, notify all absent students
        targetStudents = students.filter((student) => {
          const isAbsent = getAttendanceStatus(student.id) === "absent";
          // Student absence status checked
          return isAbsent;
        });
        targetName = t("teacher.dashboard.allStudents");
        targetType = "all";
      }

      // Found absent students to notify
      const allAbsentStudents = students.filter(
        (student) => getAttendanceStatus(student.id) === "absent"
      );

      // Room filtering logic (debug logs removed for production)

      // Check if there are any absent students to notify
      if (targetStudents.length === 0) {
        toast.info(
          t("teacher.attendance.noAbsentStudents"),
          {
            description: t("teacher.attendance.noAbsentStudentsInTarget", { target: targetName }),
          }
        );
        return;
      }

      // Sending reminders to absent students

      // Get current time for the message
      const currentTime = new Date();
      const timeString = currentTime.toLocaleTimeString("en-US", {
        hour: "numeric",
        minute: "numeric",
        hour12: true,
      });

      // Create reminder notifications for all absent students
      // Creating notifications for students

      // Check if we have students to notify
      if (targetStudents.length === 0) {
        toast.info(
          t("teacher.attendance.noRemindersSent"),
          {
            description: t("teacher.attendance.noAbsentStudentsFound"),
          }
        );
        return;
      }

      // Create localized notifications for each student
      const notificationResult = await createBulkLocalizedNotifications({
        students: targetStudents,
        teacherId: profile?.id,
        type: "attendance",
        templateKey: "attendanceReminder",
        getTemplateParams: (student) => {
          const studentRoomId = student.roomId || student.room_id;
          const studentRoom = rooms.find((r) => r.id === studentRoomId);
          const roomName = studentRoom
            ? `Room ${studentRoom.name}`
            : "your assigned room";

          return [profile?.name || "your teacher", roomName];
        },
        getMetadata: (student) => {
          const studentRoomId = student.roomId || student.room_id;
          const studentRoom = rooms.find((r) => r.id === studentRoomId);
          const roomName = studentRoom
            ? `Room ${studentRoom.name}`
            : "your assigned room";

          return {
            room_id: studentRoomId || "",
            room_name: roomName,
            notification_type: "attendance_reminder",
            reminder_time: timeString,
            target_type: targetType,
            teacher_name: profile?.name || "Teacher",
            teacher_id: profile?.id || "",
          };
        },
        getRoomNumber: (student) => {
          const studentRoomId = student.roomId || student.room_id;
          const studentRoom = rooms.find((r) => r.id === studentRoomId);
          return studentRoom ? studentRoom.name : "";
        },
      });

      // Notification creation completed

      // Update the last notification sent timestamp
      setLastNotificationSent(currentTime);

      // Show success toast with detailed information
      const successCount = notificationResult.successful;
      const allSuccessful = successCount === targetStudents.length;

      // Show a single, contextual notification
      if (allSuccessful) {
        toast.success(
          t("teacher.attendance.remindersSentSuccessfully"),
          {
            description: t("teacher.attendance.studentsNotified", {
              count: successCount,
              target: targetName,
            }) + ` • ${timeString}`,
            duration: 5000,
          }
        );
      } else {
        toast.warning(
          t("teacher.attendance.remindersPartiallySent"),
          {
            description: t("teacher.attendance.studentsNotified", {
              count: successCount,
              target: targetName,
            }) + ` • ${targetStudents.length - successCount} failed to send • ${timeString}`,
            duration: 6000,
          }
        );
      }
    } catch (error) {
      console.error("Error sending attendance reminders:", error);
      toast.error(
        t("teacher.dashboard.remindersFailed"),
        {
          description: t("teacher.dashboard.remindersFailedDescription")
        }
      );
    }
  };

  const handleRefresh = () => {
    fetchAttendanceData();
  };

  // Update fetchAttendanceData to filter students by block
  const fetchAttendanceData = async (): Promise<void> => {
    // Always fetch data, even if no specific room or block is selected
    // This ensures we always have data to display
    // Fetching attendance data with filters

    // Only show loading indicator on initial load, not during refreshes
    if (students.length === 0) {
      setIsLoading(true);
    }
    setIsRefreshing(true);

    try {
      // Get today's date at midnight
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      const todayStr = today.toISOString();
      const tomorrowStr = new Date(
        today.getTime() + 24 * 60 * 60 * 1000
      ).toISOString();

      // Fetching attendance data for date range

      // Make sure we have rooms data first
      if (rooms.length === 0) {
        // No rooms data yet, fetching rooms first

        // Only fetch rooms for the current school
        let roomsQuery = supabase.from("rooms").select("*");

        // Filter by school_id if available
        if (profile?.school_id) {
          roomsQuery = roomsQuery.eq("school_id", profile.school_id);
        }

        const { data: roomsData, error: roomsError } = await roomsQuery;

        if (roomsError) throw roomsError;
        setRooms(roomsData || []);
        // Fetched rooms from teacher's school
      }

      // Fetch only students from the teacher's school
      let query = supabase.from("profiles").select("*").eq("role", "student");

      // Filter by school_id if available
      if (profile?.school_id) {
        query = query.eq("school_id", profile.school_id);
      }

      const { data: enrolledStudents, error: enrollmentError } = await query;

      // Fetched students from database

      if (enrollmentError) throw enrollmentError;

      // Get all student IDs for the query
      const studentIds = (enrolledStudents || []).map((student) => student.id);

      if (studentIds.length === 0) {
        // No students to fetch attendance records for
        setStudents([]);
        setAttendanceRecords({});
        setTodayStats({
          present: 0,
          absent: 0,
          late: 0,
          excused: 0,
          total: 0,
        });
        return;
      }

      // IMPORTANT: Fetch TODAY'S attendance records for each student
      // Filter by today's date to show only current day attendance status
      let recordsQuery = supabase
        .from("attendance_records")
        .select("*")
        .in("student_id", studentIds)
        .gte("timestamp", todayStr)
        .lt("timestamp", tomorrowStr)
        .order("timestamp", { ascending: false });

      // Filter by school_id if available
      if (profile?.school_id) {
        recordsQuery = recordsQuery.eq("school_id", profile.school_id);
      }

      const { data: records, error: recordsError } = await recordsQuery;

      if (recordsError) throw recordsError;

      // Fetched attendance records

      // Transform student data
      const studentsList = (enrolledStudents || []).map((student) => {
        // Processing student data

        // Find the room for this student to get the block_id
        const studentRoom = rooms.find((r) => r.id === student.room_id);
        const blockId = studentRoom ? studentRoom.block_id : null;

        // Get room and block names for display
        // First try to get from the student profile directly
        let roomName = student.room_number || "";
        let blockName = student.block_name || "";

        // If not available in profile, try to get from the rooms data
        if (!roomName && studentRoom) {
          roomName = studentRoom.name || "";
        }

        if (!blockName && blockId) {
          // Get block name from blocks table
          const block = blocks.find((b) => b.id === blockId);
          blockName = block ? block.name : "";
        }

        // Student room and block assignment processed

        return {
          id: student.id,
          name: student.name || "Unknown",
          email: student.email || "",
          role: "student" as const,
          studentId: student.student_id || "",
          course: student.course || "",
          biometricRegistered: student.biometric_registered || false,
          photoUrl: student.photo_url || "",
          blockName: blockName,
          roomNumber: roomName,
          roomId: student.room_id || "",
          // Use the derived block_id from the room
          block_id: blockId || "",
        };
      });

      // Process attendance records - get the most recent record for each student
      const formattedRecords: Record<string, AttendanceRecord> = {};

      // Group records by student_id
      const recordsByStudent: Record<string, any[]> = {};
      (records || []).forEach((record) => {
        if (!recordsByStudent[record.student_id]) {
          recordsByStudent[record.student_id] = [];
        }
        recordsByStudent[record.student_id].push(record);
      });

      // For each student, get the most recent record
      // Since we already ordered by timestamp descending in the query,
      // the first record for each student should be the most recent
      studentsList.forEach((student) => {
        const studentRecords = recordsByStudent[student.id] || [];
        if (studentRecords.length > 0) {
          // The records are already sorted by timestamp (newest first)
          const mostRecent = studentRecords[0];

          formattedRecords[student.id] = {
            id: mostRecent.id,
            studentId: mostRecent.student_id,
            roomId: mostRecent.room_id,
            timestamp: mostRecent.timestamp,
            deviceInfo: mostRecent.device_info || "",
            location: mostRecent.location || null,
            verificationMethod: mostRecent.verification_method as
              | "biometric"
              | "pin"
              | "manual",
            status: mostRecent.status as
              | "present"
              | "absent"
              | "late"
              | "excused",
          };
        }
      });

      // For students without any records, set default status to absent
      const completeRecords = { ...formattedRecords };

      // For any remaining students without records, set default status to absent
      studentsList.forEach((student) => {
        if (!completeRecords[student.id]) {
          // Setting default status for student
          completeRecords[student.id] = {
            id: "", // Will be set when record is created
            studentId: student.id,
            roomId: student.roomId || selectedRoom || "",
            timestamp: new Date().toISOString(),
            deviceInfo: "",
            location: null,
            verificationMethod: "manual",
            status: "absent",
          };
        } else {
          // Using existing record for student
        }
      });

      // Update states only if we have valid data
      setStudents(studentsList);
      setAttendanceRecords(completeRecords);

      // Calculate statistics with the new data
      const newStats = calculateStats(completeRecords, studentsList);
      setTodayStats(newStats);

      // Loaded attendance data

      // Save data to localStorage for StudentDirectory to sync
      const dashboardData = {
        students: studentsList,
        attendanceRecords: completeRecords,
        timestamp: Date.now(),
        source: "TeacherDashboard"
      };
      localStorage.setItem("teacherDashboardData", JSON.stringify(dashboardData));

      // Dispatch event to notify StudentDirectory of data update
      const event = new CustomEvent("dashboard-data-updated", {
        detail: dashboardData
      });
      window.dispatchEvent(event);
      // Saved data to localStorage and dispatched event
    } catch (err) {
      console.error("Error in fetchAttendanceData:", err);
      toast.translateError(
        t,
        "common.error",
        "teacher.dashboard.fetchDataError"
      );
    } finally {
      setIsRefreshing(false);
      setIsLoading(false);
    }
  };

  // Initial data load - this runs ONCE when component mounts
  useEffect(() => {
    const loadInitialData = async () => {
      // INITIAL LOAD - fetching all students

      // Force reset filters to ensure we show all students
      setSelectedBlock(null);
      setSelectedRoom("");
      setShowOnlyAbsent(false);

      try {
        // Fetch all data
        await fetchAttendanceData();

        // We don't need to force a second refresh here anymore
        // The component will handle empty data gracefully
      } catch (error) {
        console.error("Error loading initial data:", error);
        // Use setTimeout to avoid the React state update during render issue
        setTimeout(() => {
          toast.translateError(
            t,
            "teacher.dashboard.errorLoadingData",
            "teacher.dashboard.errorLoadingDataDescription"
          );
        }, 0);
      }

      // Initial data loading complete
    };

    loadInitialData();
  }, []);

  // Reload data when filters change
  useEffect(() => {
    if (students.length > 0) {
      // Skip on first render
      // Filters changed, reloading data
      fetchAttendanceData();
    }
  }, [selectedBlock, selectedRoom, showOnlyAbsent]);

  // IMPORTANT: Make sure we have students to display
  // Total students before filtering

  // Filter students based on multiple criteria
  let filteredStudents = students.filter((student) => {
    // 1. Apply search filter (check both name and student ID)
    const query = searchQuery.toLowerCase().trim();
    const matchesSearch =
      query === "" ||
      student.name.toLowerCase().includes(query) ||
      (student.studentId && student.studentId.toLowerCase().includes(query));

    // 2. Apply absent filter if enabled
    const matchesAbsentFilter =
      !showOnlyAbsent || getAttendanceStatus(student.id) === "absent";

    // 3. Apply block filter if a block is selected
    let matchesBlockFilter = !selectedBlock; // Default to true if no block selected

    if (selectedBlock) {
      // Get the selected block name for comparison
      const selectedBlockObj = blocks.find((b) => b.id === selectedBlock);
      const selectedBlockName = selectedBlockObj?.name || "";

      // Check if the student's blockName matches the selected block name
      if (student.blockName && student.blockName.includes(selectedBlockName)) {
        matchesBlockFilter = true;
      }
      // If the student has a block_id that matches
      else if (student.block_id && student.block_id === selectedBlock) {
        matchesBlockFilter = true;
      }
      // If the student has a room that belongs to the selected block
      else if (student.room_id) {
        const studentRoom = rooms.find((r) => r.id === student.room_id);
        if (studentRoom && studentRoom.block_id === selectedBlock) {
          matchesBlockFilter = true;
        } else {
          matchesBlockFilter = false;
        }
      } else {
        matchesBlockFilter = false;
      }

      // Block filter applied
    }

    // 4. Apply room filter if a room is selected
    let matchesRoomFilter = !selectedRoom; // Default to true if no room selected

    if (selectedRoom) {
      // Get the selected room name for comparison
      const selectedRoomObj = rooms.find((r) => r.id === selectedRoom);
      const selectedRoomName = selectedRoomObj?.name || "";

      // Check if the student's roomNumber matches the selected room name
      if (student.roomNumber && student.roomNumber.includes(selectedRoomName)) {
        matchesRoomFilter = true;
      }
      // Check if the student's room_id matches the selected room
      else if (student.room_id === selectedRoom) {
        matchesRoomFilter = true;
      } else {
        matchesRoomFilter = false;
      }

      // Room filter applied
    }

    // Return true only if all filters match
    return (
      matchesSearch &&
      matchesAbsentFilter &&
      matchesBlockFilter &&
      matchesRoomFilter
    );
  });

  // We'll use this to track if we're showing a fallback view
  const [showingFallback, setShowingFallback] = useState(false);
  const [showNoMatchToast, setShowNoMatchToast] = useState(false);
  const [showNoAssignmentToast, setShowNoAssignmentToast] = useState(false);

  // Check if any students have room assignments (either by room_id or roomNumber)
  const anyStudentsHaveRooms = students.some(
    (student) => student.room_id || student.roomNumber
  );

  // Show a toast if no students are found in the selected room/block
  useEffect(() => {
    // Only show toast if we have students loaded and a filter is applied
    if (
      students.length > 0 &&
      filteredStudents.length === 0 &&
      (selectedBlock || selectedRoom) &&
      !showNoAssignmentToast &&
      !isLoading // Make sure we're not still loading data
    ) {
      setShowNoAssignmentToast(true);
      setTimeout(() => {
        toast.info(
          t("teacher.dashboard.noStudentsFound"),
          {
            description: t("teacher.dashboard.noStudentsFoundDescription"),
            duration: 8000,
          }
        );
      }, 0);
    }
  }, [
    students.length,
    filteredStudents.length,
    selectedBlock,
    selectedRoom,
    showNoAssignmentToast,
    isLoading,
  ]);

  // Determine if we need to show a fallback view
  const needsFallback =
    students.length > 0 &&
    filteredStudents.length === 0 &&
    (selectedBlock || selectedRoom);

  // Update the fallback state when needed
  useEffect(() => {
    // Only apply fallback logic if we're not loading and have students
    if (needsFallback && !isLoading) {
      setShowingFallback(true);

      // Only show the toast once when the filter changes
      if (!showNoMatchToast && !showNoAssignmentToast) {
        setShowNoMatchToast(true);
        setTimeout(() => {
          toast.info(
            t("teacher.dashboard.noStudentsFound"),
            {
              description: t("teacher.dashboard.showingAllStudents", {
                type: selectedRoom ? t("common.room") : t("common.block")
              }),
              duration: 5000,
            }
          );
        }, 0);
      }
    } else {
      setShowingFallback(false);
      setShowNoMatchToast(false);
    }
  }, [
    needsFallback,
    selectedBlock,
    selectedRoom,
    showNoMatchToast,
    showNoAssignmentToast,
    isLoading,
  ]);

  // Apply fallback filtering if needed
  const displayedStudents = needsFallback
    ? students.filter((student) => {
        const query = searchQuery.toLowerCase().trim();
        const matchesSearch =
          query === "" ||
          student.name.toLowerCase().includes(query) ||
          (student.studentId &&
            student.studentId.toLowerCase().includes(query));

        const matchesAbsentFilter =
          !showOnlyAbsent || getAttendanceStatus(student.id) === "absent";

        return matchesSearch && matchesAbsentFilter;
      })
    : filteredStudents;

  // Filtering results calculated

  // Student filtering logic completed

  return (
    <div className="space-y-6">
      {isLoading ? (
        <div className="flex justify-center items-center h-20">
          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
        </div>
      ) : (
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>{t("teacher.dashboard.statistics")}</CardTitle>
              <CardDescription>
                {t("teacher.dashboard.statisticsDescription")}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                <EnhancedStatsCard
                  title={t("teacher.dashboard.present")}
                  value={todayStats.present}
                  total={todayStats.total}
                  icon={<UserCheck className="h-4 w-4" />}
                  description={t("teacher.dashboard.studentsInClass")}
                  colorClass="green"
                />
                <EnhancedStatsCard
                  title={t("teacher.dashboard.absent")}
                  value={todayStats.absent}
                  total={todayStats.total}
                  icon={<UserX className="h-4 w-4" />}
                  description={t("teacher.dashboard.studentsMissing")}
                  colorClass="red"
                />
                <EnhancedStatsCard
                  title={t("teacher.dashboard.late")}
                  value={todayStats.late}
                  total={todayStats.total}
                  icon={<Clock className="h-4 w-4" />}
                  description={t("teacher.dashboard.studentsLate")}
                  colorClass="amber"
                />
                <EnhancedStatsCard
                  title={t("teacher.dashboard.excused")}
                  value={todayStats.excused}
                  total={todayStats.total}
                  icon={<FileCheck className="h-4 w-4" />}
                  description={t("teacher.dashboard.excusedAbsences")}
                  colorClass="blue"
                />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <div className="flex flex-col md:flex-row justify-between md:items-center">
                <div>
                  <CardTitle>{t("teacher.dashboard.roomAttendance")}</CardTitle>
                  <CardDescription>
                    {t("teacher.dashboard.manageRoomAttendance")}
                  </CardDescription>
                </div>

                {/* Mobile Controls */}
                <div className="mt-3 md:hidden">
                  <div className="grid grid-cols-2 gap-2">
                    {/* Block/Room Selector - Mobile */}
                    <div className="col-span-2">
                      {profile?.id && (
                        <BlockSelector
                          selectedRoom={selectedRoom}
                          onRoomSelect={(roomId) => {
                            setSelectedRoom(roomId);
                            fetchAttendanceData();
                          }}
                          teacherId={profile.id}
                          selectedBlock={selectedBlock}
                          onBlockSelect={(blockId) => {
                            setSelectedBlock(blockId);
                            setSelectedRoom("");
                            fetchAttendanceData();
                          }}
                        />
                      )}
                    </div>

                    {/* Refresh Button - Mobile */}
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleRefresh}
                      disabled={isRefreshing}
                      className="h-9 text-xs flex items-center justify-center"
                    >
                      <RefreshCw
                        size={14}
                        className={cn(
                          "mr-1",
                          isRefreshing ? "animate-spin" : ""
                        )}
                      />
                      <span>{t("teacher.dashboard.refresh")}</span>
                    </Button>

                    {/* Filter Dropdown - Mobile */}
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button
                          variant="outline"
                          size="sm"
                          className="h-9 text-xs w-full"
                        >
                          <ChevronDown size={14} className="mr-1" />
                          <span>{t("teacher.dashboard.options")}</span>
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent
                        align="end"
                        className="w-[calc(100vw-2rem)] p-1"
                      >
                        <DropdownMenuItem
                          onClick={() => {
                            setShowOnlyAbsent(false);
                            fetchAttendanceData();
                          }}
                          className="text-xs py-1.5"
                        >
                          <div className="flex items-center justify-between w-full">
                            <span>
                              {t("teacher.dashboard.showAllStudents")}
                            </span>
                            {!showOnlyAbsent && (
                              <Check className="h-3 w-3 text-green-500 ml-2" />
                            )}
                          </div>
                        </DropdownMenuItem>

                        <DropdownMenuItem
                          onClick={() => {
                            setShowOnlyAbsent(true);
                            fetchAttendanceData();
                          }}
                          className="text-xs py-1.5"
                        >
                          <div className="flex items-center justify-between w-full">
                            <span>{t("teacher.dashboard.showOnlyAbsent")}</span>
                            {showOnlyAbsent && (
                              <Check className="h-3 w-3 text-green-500 ml-2" />
                            )}
                          </div>
                        </DropdownMenuItem>

                        <DropdownMenuSeparator className="my-1" />

                        {/* Add Block option - Mobile */}
                        <DropdownMenuItem
                          onClick={() => {
                            toast.info(
                              t("teacher.dashboard.addBlock"),
                              {
                                description: t("teacher.dashboard.addBlockDescription"),
                              }
                            );
                          }}
                          className="text-xs py-1.5"
                        >
                          <div className="flex items-center w-full">
                            <Plus className="h-3 w-3 mr-1" />
                            <span>{t("teacher.dashboard.addBlock")}</span>
                          </div>
                        </DropdownMenuItem>

                        {/* Add Room option - Mobile */}
                        {selectedBlock && (
                          <DropdownMenuItem
                            onClick={() => {
                              toast.info(
                                t("teacher.dashboard.addRoom"),
                                {
                                  description: t("teacher.dashboard.addRoomDescription"),
                                }
                              );
                            }}
                            className="text-xs py-1.5"
                          >
                            <div className="flex items-center w-full">
                              <Plus className="h-3 w-3 mr-1" />
                              <span>{t("teacher.dashboard.addRoom")}</span>
                            </div>
                          </DropdownMenuItem>
                        )}
                      </DropdownMenuContent>
                    </DropdownMenu>

                    {/* Send Reminders Button - Mobile */}
                    <Button
                      size="sm"
                      onClick={() => {
                        fetchAttendanceData().then(() => {
                          handleNotifyAbsent();
                        });
                      }}
                      className="col-span-2 h-9 text-xs mt-1"
                    >
                      <Bell size={14} className="mr-1" />{" "}
                      <span className="truncate">
                        {t("teacher.dashboard.sendVerificationReminders")}
                      </span>
                    </Button>

                    {/* Last Sent Notification - Mobile */}
                    {lastNotificationSent && (
                      <div className="col-span-2 text-[10px] text-muted-foreground text-center mt-1">
                        {t("teacher.dashboard.lastSent", {
                          time: format(lastNotificationSent, "h:mm a"),
                        })}
                      </div>
                    )}
                  </div>
                </div>

                {/* Desktop Controls */}
                <div className="hidden md:flex md:mt-0 md:items-center md:gap-2">
                  {profile?.id && (
                    <BlockSelector
                      selectedRoom={selectedRoom}
                      onRoomSelect={(roomId) => {
                        // Room selected from BlockSelector
                        setSelectedRoom(roomId);
                        // Trigger data fetch when room changes
                        fetchAttendanceData();
                      }}
                      teacherId={profile.id}
                      selectedBlock={selectedBlock}
                      onBlockSelect={(blockId) => {
                        // Block selected from BlockSelector
                        setSelectedBlock(blockId);
                        // Clear room selection when block changes
                        setSelectedRoom("");
                        // Trigger data fetch when block changes
                        fetchAttendanceData();
                      }}
                    />
                  )}
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={handleRefresh}
                    disabled={isRefreshing}
                    className="flex items-center justify-center"
                  >
                    <RefreshCw
                      size={18}
                      className={isRefreshing ? "animate-spin" : ""}
                    />
                  </Button>
                  <div className="flex items-center gap-2">
                    <div className="flex flex-col">
                      <Button
                        className="flex items-center gap-1"
                        onClick={() => {
                          // Fetch latest data first
                          fetchAttendanceData().then(() => {
                            // Then call the notification function
                            handleNotifyAbsent();
                          });
                        }}
                      >
                        <Bell size={16} />{" "}
                        {t("teacher.dashboard.sendVerificationReminders")}
                      </Button>
                      {lastNotificationSent && (
                        <span className="text-xs text-muted-foreground mt-1">
                          {t("teacher.dashboard.lastSent", {
                            time: format(lastNotificationSent, "h:mm a"),
                          })}
                        </span>
                      )}
                    </div>

                    {/* Display/Filter Dropdown - Desktop */}
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="outline" size="icon">
                          <ChevronDown size={16} />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem
                          onClick={() => {
                            // Only toggle the absent filter
                            setShowOnlyAbsent(false);
                            // Trigger data fetch
                            fetchAttendanceData();
                          }}
                        >
                          <div className="flex items-center justify-between w-full">
                            <span>
                              {t("teacher.dashboard.showAllStudents")}
                            </span>
                            {!showOnlyAbsent && (
                              <Check className="h-4 w-4 text-green-500 ml-2" />
                            )}
                          </div>
                        </DropdownMenuItem>

                        <DropdownMenuItem
                          onClick={() => {
                            // Only toggle the absent filter
                            setShowOnlyAbsent(true);
                            // Trigger data fetch
                            fetchAttendanceData();
                          }}
                        >
                          <div className="flex items-center justify-between w-full">
                            <span>{t("teacher.dashboard.showOnlyAbsent")}</span>
                            {showOnlyAbsent && (
                              <Check className="h-4 w-4 text-green-500 ml-2" />
                            )}
                          </div>
                        </DropdownMenuItem>

                        <DropdownMenuSeparator />

                        {/* Add Block option */}
                        <DropdownMenuItem
                          onClick={() => {
                            // Open dialog to add a new block
                            // This would typically be handled by the BlockSelector
                            // We'll just show a toast for now
                            toast.info(
                              t("teacher.dashboard.addBlock"),
                              {
                                description: t("teacher.dashboard.addBlockDescription"),
                              }
                            );
                          }}
                        >
                          <div className="flex items-center w-full">
                            <Plus className="h-4 w-4 mr-2" />
                            <span>{t("teacher.dashboard.addBlock")}</span>
                          </div>
                        </DropdownMenuItem>

                        {/* Add Room option (only if a block is selected) */}
                        {selectedBlock && (
                          <DropdownMenuItem
                            onClick={() => {
                              // Open dialog to add a new room
                              // This would typically be handled by the BlockSelector
                              // We'll just show a toast for now
                              toast.info(
                                t("teacher.dashboard.addRoom"),
                                {
                                  description: t("teacher.dashboard.addRoomDescription"),
                                }
                              );
                            }}
                          >
                            <div className="flex items-center w-full">
                              <Plus className="h-4 w-4 mr-2" />
                              <span>{t("teacher.dashboard.addRoom")}</span>
                            </div>
                          </DropdownMenuItem>
                        )}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </div>
              </div>
            </CardHeader>
            {/* Always show the student list */}
            <>
              <AttendanceExport
                roomId={selectedRoom}
                roomName={
                  selectedRoom
                    ? rooms.find((r) => r.id === selectedRoom)?.name
                    : "All Rooms"
                }
              />
              <div className="px-4 pb-4">
                <Card className="col-span-1 md:col-span-1">
                  <CardHeader className="flex flex-col space-y-3 pb-2 md:flex-row md:items-center md:justify-between md:space-y-0">
                    <div className="space-y-1">
                      <CardTitle>
                        {t("teacher.dashboard.studentDirectory")}
                      </CardTitle>
                      <CardDescription>
                        {showOnlyAbsent
                          ? t("teacher.dashboard.studentsNeedVerification")
                          : t("teacher.dashboard.manageStudentAttendance")}
                      </CardDescription>

                      {/* Active filter indicator */}
                      {(selectedBlock ||
                        selectedRoom ||
                        showOnlyAbsent ||
                        searchQuery) && (
                        <div className="flex items-center gap-2 mt-2">
                          <span className="text-xs text-muted-foreground">
                            {t("teacher.dashboard.activeFilters")}
                          </span>
                          <div className="flex flex-wrap gap-1">
                            {selectedBlock && (
                              <Badge variant="outline" className="text-xs">
                                {t("teacher.dashboard.block", {
                                  name:
                                    blocks.find((b) => b.id === selectedBlock)
                                      ?.name || selectedBlock.substring(0, 8),
                                })}
                                {showingFallback &&
                                  ` (${t("teacher.dashboard.showingAll")})`}
                              </Badge>
                            )}
                            {selectedRoom && (
                              <Badge variant="outline" className="text-xs">
                                {`${t("common.room")}: ${
                                  rooms.find((r) => r.id === selectedRoom)?.name ||
                                  selectedRoom.substring(0, 8)
                                }`}
                                {showingFallback &&
                                  ` (${t("teacher.dashboard.showingAll")})`}
                              </Badge>
                            )}
                            {showOnlyAbsent && (
                              <Badge variant="outline" className="text-xs">
                                {t("teacher.dashboard.absentOnly")}
                              </Badge>
                            )}
                            {searchQuery && (
                              <Badge
                                variant="outline"
                                className="text-xs flex items-center gap-1"
                              >
                                <Search className="h-3 w-3" />
                                {`${t("common.search")}: ${
                                  searchQuery.length > 15
                                    ? `${searchQuery.substring(0, 15)}...`
                                    : searchQuery
                                }`}
                              </Badge>
                            )}
                          </div>
                        </div>
                      )}
                    </div>

                    {/* Mobile Controls */}
                    <div className="grid grid-cols-2 gap-2 md:hidden">
                      {/* Search Input - Mobile */}
                      <div className="col-span-2 relative">
                        <Search className="absolute left-2.5 top-2.5 h-3 w-3 text-muted-foreground" />
                        <Input
                          placeholder={t("teacher.dashboard.searchPlaceholder")}
                          value={searchQuery}
                          onChange={(e) => setSearchQuery(e.target.value)}
                          className="pl-7 pr-7 w-full h-9 text-xs"
                        />
                        {searchQuery && (
                          <button
                            onClick={() => setSearchQuery("")}
                            className="absolute right-2.5 top-2.5 h-3 w-3 text-muted-foreground hover:text-foreground"
                            aria-label="Clear search"
                          >
                            <X size={12} />
                          </button>
                        )}
                      </div>

                      {/* Show Only Absent Toggle - Mobile */}
                      <div className="flex items-center justify-between border rounded-md px-2 py-1.5">
                        <Label
                          htmlFor="show-absent-mobile"
                          className="text-xs cursor-pointer"
                        >
                          {t("teacher.dashboard.showOnlyAbsent").toLowerCase()}
                        </Label>
                        <Switch
                          id="show-absent-mobile"
                          checked={showOnlyAbsent}
                          onCheckedChange={setShowOnlyAbsent}
                          className="scale-75"
                        />
                      </div>

                      {/* Grid/List View Toggle - Mobile */}
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          const newMode = viewMode === "list" ? "grid" : "list";
                          setViewMode(newMode);
                          localStorage.setItem(
                            "teacherDashboardViewMode",
                            newMode
                          );
                        }}
                        className="h-9 text-xs flex items-center justify-center gap-1"
                      >
                        {viewMode === "list" ? (
                          <>
                            <LayoutGrid className="h-3 w-3" />
                            <span>{t("teacher.dashboard.gridView")}</span>
                          </>
                        ) : (
                          <>
                            <LayoutList className="h-3 w-3" />
                            <span>{t("teacher.dashboard.listView")}</span>
                          </>
                        )}
                      </Button>
                    </div>

                    {/* Desktop Controls */}
                    <div className="hidden md:flex md:items-center md:gap-4">
                      <div className="flex items-center space-x-2">
                        <Switch
                          id="show-absent"
                          checked={showOnlyAbsent}
                          onCheckedChange={setShowOnlyAbsent}
                        />
                        <Label
                          htmlFor="show-absent"
                          className="text-sm cursor-pointer"
                        >
                          {t("teacher.dashboard.showOnlyAbsent").toLowerCase()}
                        </Label>
                      </div>

                      {/* Grid/List View Toggle - Desktop */}
                      <Button
                        variant="outline"
                        size="icon"
                        onClick={() => {
                          const newMode = viewMode === "list" ? "grid" : "list";
                          setViewMode(newMode);
                          // Save preference to localStorage
                          localStorage.setItem(
                            "teacherDashboardViewMode",
                            newMode
                          );
                        }}
                        className="h-9 w-9"
                        title={
                          viewMode === "list"
                            ? "Switch to Grid View"
                            : "Switch to List View"
                        }
                      >
                        {viewMode === "list" ? (
                          <LayoutGrid className="h-4 w-4" />
                        ) : (
                          <LayoutList className="h-4 w-4" />
                        )}
                      </Button>

                      {/* Search Input - Desktop */}
                      <div className="relative w-[250px]">
                        <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                        <Input
                          placeholder={t("teacher.dashboard.searchPlaceholder")}
                          value={searchQuery}
                          onChange={(e) => setSearchQuery(e.target.value)}
                          className="pl-8 pr-8 w-full"
                        />
                        {searchQuery && (
                          <button
                            onClick={() => setSearchQuery("")}
                            className="absolute right-2.5 top-2.5 h-4 w-4 text-muted-foreground hover:text-foreground"
                            aria-label="Clear search"
                          >
                            <X size={16} />
                          </button>
                        )}
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    {isLoading ? (
                      <div className="flex items-center justify-center py-6">
                        <Loader2 className="h-6 w-6 animate-spin" />
                      </div>
                    ) : students.length > 0 &&
                      filteredStudents.length === 0 &&
                      (selectedBlock || selectedRoom) ? (
                      <div className="flex flex-col items-center justify-center py-8 text-center">
                        <AlertTriangle className="h-12 w-12 text-amber-500 mb-4" />
                        <h3 className="text-lg font-medium">
                          {t("teacher.dashboard.noStudentsFound")}
                        </h3>
                        <p className="text-sm text-muted-foreground mt-1 max-w-md">
                          {t("teacher.dashboard.noStudentsInRoom")}
                        </p>
                        <div className="mt-4 flex gap-2">
                          <Button
                            variant="outline"
                            onClick={() => {
                              setSelectedBlock(null);
                              setSelectedRoom("");
                            }}
                          >
                            {t("teacher.dashboard.clearFilters")}
                          </Button>
                          <Button
                            variant="default"
                            onClick={() => {
                              toast.info(
                                t("teacher.dashboard.howToAssignRooms"),
                                {
                                  description: t("teacher.dashboard.studentRoomAssignment"),
                                  duration: 5000,
                                }
                              );
                            }}
                          >
                            {t("teacher.dashboard.howToAssignRooms")}
                          </Button>
                        </div>
                      </div>
                    ) : students.length > 0 &&
                      displayedStudents.length === 0 ? (
                      <div className="flex flex-col items-center justify-center py-8 text-center">
                        <UserCheck className="h-12 w-12 text-muted-foreground mb-4" />
                        <h3 className="text-lg font-medium">
                          {t("teacher.dashboard.noStudentsToDisplay")}
                        </h3>
                        <p className="text-sm text-muted-foreground mt-1">
                          {showOnlyAbsent
                            ? t("teacher.dashboard.noAbsentStudents")
                            : t("teacher.dashboard.noStudentsMatchingSearch")}
                        </p>
                      </div>
                    ) : displayedStudents.length > 0 ? (
                      viewMode === "list" ? (
                        <StudentListView
                          students={displayedStudents}
                          getAttendanceStatus={getAttendanceStatus}
                          getStatusIcon={getStatusIcon}
                          selectedRoom={selectedRoom || ""}
                          onStatusUpdate={handleStatusUpdate}
                        />
                      ) : (
                        <StudentGridView
                          students={displayedStudents}
                          getAttendanceStatus={getAttendanceStatus}
                          getStatusIcon={getStatusIcon}
                          selectedRoom={selectedRoom || ""}
                          onStatusUpdate={handleStatusUpdate}
                        />
                      )
                    ) : (
                      // Default view when no students are loaded yet
                      <div className="flex flex-col items-center justify-center py-8 text-center">
                        <Users className="h-12 w-12 text-muted-foreground mb-4" />
                        <h3 className="text-lg font-medium">
                          {t("teacher.dashboard.loadingStudents")}
                        </h3>
                        <p className="text-sm text-muted-foreground mt-1">
                          {t("teacher.dashboard.pleaseWaitLoading")}
                        </p>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>
            </>
          </Card>
        </div>
      )}
    </div>
  );
}
