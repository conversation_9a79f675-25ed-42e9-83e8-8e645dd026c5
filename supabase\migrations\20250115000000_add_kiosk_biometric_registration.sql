-- Add RPC function for kiosk biometric registration
-- This allows kiosk devices to register biometrics for students without requiring authentication

-- <PERSON>reate function to register biometrics from kiosk
CREATE OR REPLACE FUNCTION register_biometric_from_kiosk(
  target_user_id UUID,
  credential_id_param TEXT,
  public_key_param TEXT,
  school_id_param UUID
)
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  student_profile RECORD;
  result JSON;
BEGIN
  -- Verify the student exists and belongs to the specified school
  SELECT * INTO student_profile
  FROM profiles
  WHERE user_id = target_user_id
    AND school_id = school_id_param
    AND role = 'student';
    
  IF NOT FOUND THEN
    RAISE EXCEPTION 'Student not found or not in specified school';
  END IF;
  
  -- Insert the biometric credential (bypassing RLS)
  INSERT INTO biometric_credentials (
    user_id,
    credential_id,
    public_key,
    counter,
    created_at
  ) VALUES (
    target_user_id,
    credential_id_param,
    public_key_param,
    0,
    NOW()
  );
  
  -- Update the profile to mark biometric as registered
  UPDATE profiles
  SET biometric_registered = true
  WHERE user_id = target_user_id;
  
  -- Return success result
  result := json_build_object(
    'success', true,
    'message', 'Biometric credential registered successfully',
    'user_id', target_user_id
  );
  
  RETURN result;
  
EXCEPTION
  WHEN OTHERS THEN
    -- Return error result
    result := json_build_object(
      'success', false,
      'error', SQLERRM
    );
    RETURN result;
END;
$$;

-- Grant execute permission to authenticated users (kiosk tablets will be authenticated)
GRANT EXECUTE ON FUNCTION register_biometric_from_kiosk TO authenticated;
