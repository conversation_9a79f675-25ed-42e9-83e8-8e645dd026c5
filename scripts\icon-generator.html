<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Icon Generator for iOS PWA</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .icon-preview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .icon-item {
            text-align: center;
            padding: 15px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            background: #fafafa;
        }
        .icon-item canvas {
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-bottom: 10px;
        }
        .download-btn {
            background: #EE0D09;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }
        .download-btn:hover {
            background: #cc0b08;
        }
        .instructions {
            background: #e8f4fd;
            border: 1px solid #b8daff;
            border-radius: 6px;
            padding: 20px;
            margin: 20px 0;
        }
        .logo-svg {
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 iOS PWA Icon Generator</h1>
        <p>This tool generates PNG icons from the SVG logo for iOS PWA compatibility.</p>
        
        <div class="instructions">
            <h3>📋 Instructions:</h3>
            <ol>
                <li>Click "Generate All Icons" button below</li>
                <li>Download each PNG file by clicking the download buttons</li>
                <li>Save them to the <code>public/</code> folder of your project</li>
                <li>The app icon should now appear correctly on iPhones!</li>
            </ol>
        </div>

        <button onclick="generateAllIcons()" style="background: #28a745; color: white; border: none; padding: 15px 30px; border-radius: 6px; font-size: 16px; cursor: pointer; margin: 20px 0;">
            🚀 Generate All Icons
        </button>

        <div id="iconContainer" class="icon-preview"></div>

        <!-- Hidden SVG for conversion -->
        <svg id="logoSvg" class="logo-svg" width="200" height="200" viewBox="0 0 200 200" fill="none" xmlns="http://www.w3.org/2000/svg">
            <!-- Background circle -->
            <circle cx="100" cy="100" r="90" fill="#EE0D09" stroke="#08194A" stroke-width="8"/>
            
            <!-- Attendance tracking clipboard -->
            <g transform="translate(50, 40)">
                <!-- Clipboard base -->
                <rect x="10" y="20" width="80" height="120" rx="8" fill="#ffffff" stroke="#08194A" stroke-width="4"/>
                
                <!-- Clipboard clip -->
                <rect x="30" y="10" width="40" height="20" rx="4" fill="#08194A"/>
                
                <!-- Header line -->
                <rect x="20" y="40" width="60" height="3" rx="1.5" fill="#08194A"/>
                
                <!-- Attendance checkmarks -->
                <g stroke="#EE0D09" stroke-width="6" fill="none" stroke-linecap="round" stroke-linejoin="round">
                    <!-- First checkmark -->
                    <path d="M20 65l8 8 16-16"/>
                    <!-- Second checkmark -->
                    <path d="M20 85l8 8 16-16"/>
                    <!-- Third checkmark -->
                    <path d="M20 105l8 8 16-16"/>
                    <!-- Fourth checkmark -->
                    <path d="M20 125l8 8 16-16"/>
                </g>
                
                <!-- Student lines (representing names) -->
                <g fill="#08194A">
                    <rect x="50" y="62" width="25" height="2" rx="1"/>
                    <rect x="50" y="82" width="30" height="2" rx="1"/>
                    <rect x="50" y="102" width="20" height="2" rx="1"/>
                    <rect x="50" y="122" width="28" height="2" rx="1"/>
                </g>
            </g>
        </svg>
    </div>

    <script>
        const iconSizes = [
            { name: 'apple-touch-icon', size: 180, description: 'iOS App Icon' },
            { name: 'apple-touch-icon-152x152', size: 152, description: 'iPad Retina' },
            { name: 'apple-touch-icon-144x144', size: 144, description: 'iPad Retina' },
            { name: 'apple-touch-icon-120x120', size: 120, description: 'iPhone Retina' },
            { name: 'apple-touch-icon-114x114', size: 114, description: 'iPhone 4' },
            { name: 'apple-touch-icon-76x76', size: 76, description: 'iPad' },
            { name: 'apple-touch-icon-72x72', size: 72, description: 'iPad' },
            { name: 'apple-touch-icon-60x60', size: 60, description: 'iPhone' },
            { name: 'apple-touch-icon-57x57', size: 57, description: 'iPhone' },
            { name: 'android-chrome-192x192', size: 192, description: 'Android Chrome' },
            { name: 'android-chrome-512x512', size: 512, description: 'Android Chrome' },
            { name: 'favicon-32x32', size: 32, description: 'Favicon' },
            { name: 'favicon-16x16', size: 16, description: 'Favicon' },
        ];

        function svgToPng(svgElement, size, callback) {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            const img = new Image();
            
            canvas.width = size;
            canvas.height = size;
            
            // Convert SVG to data URL
            const svgData = new XMLSerializer().serializeToString(svgElement);
            const svgBlob = new Blob([svgData], { type: 'image/svg+xml;charset=utf-8' });
            const url = URL.createObjectURL(svgBlob);
            
            img.onload = function() {
                ctx.clearRect(0, 0, size, size);
                ctx.drawImage(img, 0, 0, size, size);
                URL.revokeObjectURL(url);
                callback(canvas);
            };
            
            img.src = url;
        }

        function downloadCanvas(canvas, filename) {
            const link = document.createElement('a');
            link.download = filename;
            link.href = canvas.toDataURL('image/png');
            link.click();
        }

        function generateAllIcons() {
            const container = document.getElementById('iconContainer');
            const svgElement = document.getElementById('logoSvg');
            
            container.innerHTML = '<p>🔄 Generating icons...</p>';
            
            let completed = 0;
            const total = iconSizes.length;
            
            iconSizes.forEach(icon => {
                svgToPng(svgElement, icon.size, (canvas) => {
                    const iconItem = document.createElement('div');
                    iconItem.className = 'icon-item';
                    
                    const previewCanvas = canvas.cloneNode();
                    const previewCtx = previewCanvas.getContext('2d');
                    const displaySize = Math.min(icon.size, 80);
                    
                    previewCanvas.width = displaySize;
                    previewCanvas.height = displaySize;
                    previewCtx.drawImage(canvas, 0, 0, displaySize, displaySize);
                    
                    iconItem.innerHTML = `
                        ${previewCanvas.outerHTML}
                        <div><strong>${icon.name}.png</strong></div>
                        <div>${icon.size}×${icon.size}</div>
                        <div style="font-size: 11px; color: #666;">${icon.description}</div>
                        <button class="download-btn" onclick="downloadCanvas(arguments[0], '${icon.name}.png')" data-canvas="${canvas.toDataURL()}">
                            📥 Download
                        </button>
                    `;
                    
                    // Store canvas data for download
                    const btn = iconItem.querySelector('.download-btn');
                    btn.addEventListener('click', () => {
                        downloadCanvas(canvas, `${icon.name}.png`);
                    });
                    
                    if (completed === 0) {
                        container.innerHTML = '';
                    }
                    container.appendChild(iconItem);
                    
                    completed++;
                    if (completed === total) {
                        const successMsg = document.createElement('div');
                        successMsg.innerHTML = `
                            <div style="background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 6px; margin: 20px 0;">
                                <strong>✅ Success!</strong> All ${total} icons generated. Download them and place in your <code>public/</code> folder.
                            </div>
                        `;
                        container.appendChild(successMsg);
                    }
                });
            });
        }
    </script>
</body>
</html>
