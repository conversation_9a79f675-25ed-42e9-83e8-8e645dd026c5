import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Tabs, Tabs<PERSON>ontent, <PERSON>bs<PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  <PERSON><PERSON>hart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell
} from 'recharts';
import {
  Monitor,
  Users,
  CheckCircle,
  XCircle,
  Clock,
  AlertTriangle,
  Download,
  RefreshCw,
  Plus,
  Eye,
  Settings,
  Activity,
  TrendingUp,
  Calendar,
  MapPin
} from "lucide-react";
import { useAuth } from "@/context/AuthContext";
import { useToast } from "@/hooks/use-toast";
import { useTranslation } from "react-i18next";
import { supabase } from "@/lib/supabase";

interface Device {
  id: string;
  deviceId: string;
  deviceName: string;
  deviceType: string;
  roomName?: string;
  isActive: boolean;
  lastSeen: string;
  registeredStudentsCount: number;
  totalStudents: number;
  registrationPercentage: number;
}

interface RegistrationSession {
  id: string;
  deviceName: string;
  sessionName: string;
  totalStudents: number;
  registeredStudents: number;
  failedRegistrations: number;
  sessionStatus: string;
  startedAt: string;
  completedAt?: string;
}

interface RegistrationStats {
  totalDevices: number;
  activeDevices: number;
  totalStudents: number;
  registeredStudents: number;
  registrationPercentage: number;
  recentSessions: number;
}

export default function RegistrationManagementDashboard() {
  const [devices, setDevices] = useState<Device[]>([]);
  const [sessions, setSessions] = useState<RegistrationSession[]>([]);
  const [stats, setStats] = useState<RegistrationStats>({
    totalDevices: 0,
    activeDevices: 0,
    totalStudents: 0,
    registeredStudents: 0,
    registrationPercentage: 0,
    recentSessions: 0
  });
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  const { user, profile } = useAuth();
  const { toast } = useToast();
  const { t } = useTranslation();

  useEffect(() => {
    loadDashboardData();
  }, [profile]);

  const loadDashboardData = async () => {
    if (!profile?.school_id) return;

    try {
      setLoading(true);
      await Promise.all([
        loadDevices(),
        loadSessions(),
        loadStats()
      ]);
    } catch (error) {
      console.error('Error loading dashboard data:', error);
      toast({
        title: t('common.error'),
        description: t("tablet.biometricRegistration.managementDashboard.loadError"),
        variant: 'destructive'
      });
    } finally {
      setLoading(false);
    }
  };

  const loadDevices = async () => {
    if (!profile?.school_id) return;

    const { data, error } = await supabase
      .from('device_registry')
      .select(`
        *,
        rooms (name)
      `)
      .eq('school_id', profile.school_id)
      .order('last_seen', { ascending: false });

    if (error) throw error;

    // Get total students count for percentage calculation
    const { count: totalStudents } = await supabase
      .from('profiles')
      .select('*', { count: 'exact', head: true })
      .eq('school_id', profile.school_id)
      .eq('role', 'student');

    const formattedDevices: Device[] = data.map(device => ({
      id: device.id,
      deviceId: device.device_id,
      deviceName: device.device_name,
      deviceType: device.device_type,
      roomName: device.rooms?.name,
      isActive: device.is_active,
      lastSeen: device.last_seen,
      registeredStudentsCount: device.registered_students_count || 0,
      totalStudents: totalStudents || 0,
      registrationPercentage: totalStudents > 0 
        ? Math.round(((device.registered_students_count || 0) / totalStudents) * 100)
        : 0
    }));

    setDevices(formattedDevices);
  };

  const loadSessions = async () => {
    if (!profile?.school_id) return;

    const { data, error } = await supabase
      .from('registration_sessions')
      .select('*')
      .eq('school_id', profile.school_id)
      .order('created_at', { ascending: false })
      .limit(10);

    if (error) throw error;

    const formattedSessions: RegistrationSession[] = data.map(session => ({
      id: session.id,
      deviceName: session.device_name,
      sessionName: session.session_name,
      totalStudents: session.total_students,
      registeredStudents: session.registered_students,
      failedRegistrations: session.failed_registrations,
      sessionStatus: session.session_status,
      startedAt: session.started_at,
      completedAt: session.completed_at
    }));

    setSessions(formattedSessions);
  };

  const loadStats = async () => {
    if (!profile?.school_id) return;

    // Get device stats
    const { data: deviceData, error: deviceError } = await supabase
      .from('device_registry')
      .select('is_active, registered_students_count')
      .eq('school_id', profile.school_id);

    if (deviceError) throw deviceError;

    // Get total students
    const { count: totalStudents } = await supabase
      .from('profiles')
      .select('*', { count: 'exact', head: true })
      .eq('school_id', profile.school_id)
      .eq('role', 'student');

    // Get recent sessions count
    const oneWeekAgo = new Date();
    oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);

    const { count: recentSessions } = await supabase
      .from('registration_sessions')
      .select('*', { count: 'exact', head: true })
      .eq('school_id', profile.school_id)
      .gte('created_at', oneWeekAgo.toISOString());

    // Calculate unique registered students across all devices
    const { data: credentialData, error: credentialError } = await supabase
      .from('biometric_credentials')
      .select('user_id')
      .in('device_id', deviceData.map(d => d.device_id));

    if (credentialError) throw credentialError;

    const uniqueRegisteredStudents = new Set(credentialData.map(c => c.user_id)).size;

    const totalDevices = deviceData.length;
    const activeDevices = deviceData.filter(d => d.is_active).length;
    const registrationPercentage = totalStudents > 0 
      ? Math.round((uniqueRegisteredStudents / totalStudents) * 100)
      : 0;

    setStats({
      totalDevices,
      activeDevices,
      totalStudents: totalStudents || 0,
      registeredStudents: uniqueRegisteredStudents,
      registrationPercentage,
      recentSessions: recentSessions || 0
    });
  };

  const refreshData = async () => {
    setRefreshing(true);
    await loadDashboardData();
    setRefreshing(false);
    toast({
      title: t('common.success'),
      description: t("tablet.biometricRegistration.managementDashboard.refreshSuccess")
    });
  };

  const getStatusBadge = (status: string) => {
    const variants = {
      'active': 'default',
      'completed': 'default',
      'cancelled': 'destructive'
    } as const;

    return (
      <Badge variant={variants[status as keyof typeof variants] || 'outline'}>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    );
  };

  const chartData = devices.map(device => ({
    name: device.deviceName,
    registered: device.registeredStudentsCount,
    total: device.totalStudents,
    percentage: device.registrationPercentage
  }));

  const pieData = [
    { name: t("tablet.biometricRegistration.managementDashboard.analytics.chartLabels.registered"), value: stats.registeredStudents, color: '#10b981' },
    { name: t("tablet.biometricRegistration.managementDashboard.analytics.chartLabels.notRegistered"), value: stats.totalStudents - stats.registeredStudents, color: '#ef4444' }
  ];

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p>{t("tablet.biometricRegistration.managementDashboard.loading")}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">{t("tablet.biometricRegistration.managementDashboard.title")}</h2>
          <p className="text-muted-foreground">
            {t("tablet.biometricRegistration.managementDashboard.subtitle")}
          </p>
        </div>

        <Button onClick={refreshData} disabled={refreshing}>
          <RefreshCw className={`w-4 h-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
          {t("tablet.biometricRegistration.managementDashboard.refresh")}
        </Button>
      </div>

      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t("tablet.biometricRegistration.managementDashboard.stats.totalDevices")}</CardTitle>
            <Monitor className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalDevices}</div>
            <p className="text-xs text-muted-foreground">
              {t("tablet.biometricRegistration.managementDashboard.stats.activeDevices", { count: stats.activeDevices })}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t("tablet.biometricRegistration.managementDashboard.stats.totalStudents")}</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalStudents}</div>
            <p className="text-xs text-muted-foreground">
              {t("tablet.biometricRegistration.managementDashboard.stats.inYourSchool")}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t("tablet.biometricRegistration.managementDashboard.stats.registeredStudents")}</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.registeredStudents}</div>
            <p className="text-xs text-muted-foreground">
              {t("tablet.biometricRegistration.managementDashboard.stats.registrationRate", { percentage: stats.registrationPercentage })}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t("tablet.biometricRegistration.managementDashboard.stats.recentSessions")}</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.recentSessions}</div>
            <p className="text-xs text-muted-foreground">
              {t("tablet.biometricRegistration.managementDashboard.stats.lastSevenDays")}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Main Content Tabs */}
      <Tabs defaultValue="devices" className="space-y-4">
        <TabsList>
          <TabsTrigger value="devices">{t("tablet.biometricRegistration.managementDashboard.tabs.devices")}</TabsTrigger>
          <TabsTrigger value="sessions">{t("tablet.biometricRegistration.managementDashboard.tabs.sessions")}</TabsTrigger>
          <TabsTrigger value="analytics">{t("tablet.biometricRegistration.managementDashboard.tabs.analytics")}</TabsTrigger>
        </TabsList>

        <TabsContent value="devices" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>{t("tablet.biometricRegistration.managementDashboard.devices.title")}</CardTitle>
              <CardDescription>
                {t("tablet.biometricRegistration.managementDashboard.devices.description")}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>{t("tablet.biometricRegistration.managementDashboard.devices.headers.device")}</TableHead>
                    <TableHead>{t("tablet.biometricRegistration.managementDashboard.devices.headers.room")}</TableHead>
                    <TableHead>{t("tablet.biometricRegistration.managementDashboard.devices.headers.status")}</TableHead>
                    <TableHead>{t("tablet.biometricRegistration.managementDashboard.devices.headers.students")}</TableHead>
                    <TableHead>{t("tablet.biometricRegistration.managementDashboard.devices.headers.progress")}</TableHead>
                    <TableHead>{t("tablet.biometricRegistration.managementDashboard.devices.headers.lastSeen")}</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {devices.map((device) => (
                    <TableRow key={device.id}>
                      <TableCell>
                        <div>
                          <div className="font-medium">{device.deviceName}</div>
                          <div className="text-sm text-muted-foreground">{device.deviceId}</div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-1">
                          <MapPin className="w-3 h-3 text-muted-foreground" />
                          {device.roomName || t("tablet.biometricRegistration.managementDashboard.devices.labels.unassigned")}
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant={device.isActive ? 'default' : 'secondary'}>
                          {device.isActive ? t("tablet.biometricRegistration.managementDashboard.devices.status.active") : t("tablet.biometricRegistration.managementDashboard.devices.status.inactive")}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm">
                          <div>{device.registeredStudentsCount} / {device.totalStudents}</div>
                          <div className="text-muted-foreground">{t("tablet.biometricRegistration.managementDashboard.devices.labels.registered")}</div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="space-y-1">
                          <Progress value={device.registrationPercentage} className="h-2" />
                          <div className="text-xs text-muted-foreground">
                            {device.registrationPercentage}%
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm text-muted-foreground">
                          {new Date(device.lastSeen).toLocaleString()}
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="sessions" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>{t("tablet.biometricRegistration.managementDashboard.sessions.title")}</CardTitle>
              <CardDescription>
                {t("tablet.biometricRegistration.managementDashboard.sessions.description")}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>{t("tablet.biometricRegistration.managementDashboard.sessions.headers.session")}</TableHead>
                    <TableHead>{t("tablet.biometricRegistration.managementDashboard.sessions.headers.device")}</TableHead>
                    <TableHead>{t("tablet.biometricRegistration.managementDashboard.sessions.headers.status")}</TableHead>
                    <TableHead>{t("tablet.biometricRegistration.managementDashboard.sessions.headers.progress")}</TableHead>
                    <TableHead>{t("tablet.biometricRegistration.managementDashboard.sessions.headers.started")}</TableHead>
                    <TableHead>{t("tablet.biometricRegistration.managementDashboard.sessions.headers.completed")}</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {sessions.map((session) => (
                    <TableRow key={session.id}>
                      <TableCell>
                        <div className="font-medium">{session.sessionName}</div>
                      </TableCell>
                      <TableCell>{session.deviceName}</TableCell>
                      <TableCell>
                        {getStatusBadge(session.sessionStatus)}
                      </TableCell>
                      <TableCell>
                        <div className="space-y-1">
                          <div className="text-sm">
                            {session.registeredStudents} / {session.totalStudents}
                          </div>
                          <Progress
                            value={(session.registeredStudents / session.totalStudents) * 100}
                            className="h-2"
                          />
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm text-muted-foreground">
                          {new Date(session.startedAt).toLocaleString()}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm text-muted-foreground">
                          {session.completedAt
                            ? new Date(session.completedAt).toLocaleString()
                            : '-'
                          }
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>{t("tablet.biometricRegistration.managementDashboard.analytics.title")}</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart data={chartData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" />
                    <YAxis />
                    <Tooltip />
                    <Bar dataKey="registered" fill="#10b981" name={t("tablet.biometricRegistration.managementDashboard.analytics.chartLabels.registered")} />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>{t("tablet.biometricRegistration.managementDashboard.analytics.overallRegistrationStatus")}</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <PieChart>
                    <Pie
                      data={pieData}
                      cx="50%"
                      cy="50%"
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                      label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                    >
                      {pieData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </PieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>{t("tablet.biometricRegistration.managementDashboard.analytics.registrationSummary")}</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span>{t("tablet.biometricRegistration.managementDashboard.analytics.overallRegistrationProgress")}</span>
                  <span className="font-medium">{stats.registrationPercentage}%</span>
                </div>
                <Progress value={stats.registrationPercentage} className="h-3" />

                <div className="grid grid-cols-2 gap-4 pt-4">
                  <div className="text-center p-4 bg-green-50 rounded-lg">
                    <div className="text-2xl font-bold text-green-600">{stats.registeredStudents}</div>
                    <div className="text-sm text-green-700">{t("tablet.biometricRegistration.managementDashboard.analytics.studentsRegistered")}</div>
                  </div>
                  <div className="text-center p-4 bg-red-50 rounded-lg">
                    <div className="text-2xl font-bold text-red-600">
                      {stats.totalStudents - stats.registeredStudents}
                    </div>
                    <div className="text-sm text-red-700">{t("tablet.biometricRegistration.managementDashboard.analytics.studentsRemaining")}</div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
